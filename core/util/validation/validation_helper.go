package validation

import (
	"encoding/json"
	"fmt"
	"github.com/go-playground/locales"
	"github.com/go-playground/locales/en"
	"github.com/go-playground/locales/id"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	en_translations "github.com/go-playground/validator/v10/translations/en"
	id_translations "github.com/go-playground/validator/v10/translations/id"
	"log"
	"reflect"
	"strings"
)

var validate *validator.Validate
var Language = "id"
var ErrorList map[string]interface{}

type Lang struct {
	Trans ut.Translator
	ID string
	EN string
}

func New() (*validator.Validate, ut.Translator) {
	v := validator.New()
	t := Translator(v)
	return v, t
}

func Translator(validate *validator.Validate) ut.Translator {
	//translation of validation
	var lang locales.Translator
	var trans ut.Translator
	var transFound bool

	switch Language {
	case "id":
		lang = id.New()
		uni := ut.New(lang, lang)
		trans, transFound = uni.GetTranslator(Language)
		if !transFound {
			log.Fatalf("translator %s not found. \n", Language)
		}
		id_translations.RegisterDefaultTranslations(validate, trans)
	case "en":
		lang = en.New()
		uni := ut.New(lang, lang)
		trans, transFound = uni.GetTranslator(Language)
		if !transFound {
			log.Fatalf("translator %s not found. \n", Language)
		}
		en_translations.RegisterDefaultTranslations(validate, trans)
	default:
		lang = id.New()
		uni := ut.New(lang, lang)
		trans, transFound = uni.GetTranslator("id")
		if !transFound {
			log.Fatalf("translator default not found: %s \n", Language)
		}
		id_translations.RegisterDefaultTranslations(validate, trans)
	}

	return trans
}

func (l Lang) RegisterTranslation(validate *validator.Validate, key string, override ...bool) Lang {
	trans := l.Trans
	ovrd := false
	if override != nil {
		ovrd = override[0]
	}
	v := validate
	v.RegisterTranslation(key, trans, func(ut ut.Translator) error {
		switch Language {
		case "id":
			return ut.Add(key, l.ID, ovrd)
		case "en":
			return ut.Add(key, l.EN, ovrd)
		}
		return ut.Add(key, l.ID, ovrd)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T(key, fe.Field())
		return t
	})

	return l
}
func (l Lang) Struct(validate *validator.Validate, structData interface{}) map[string]interface{}  {
	trans := l.Trans

	// register function to get tag name from json tags.
	validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})

	//validation
	err := validate.Struct(structData)
	errList := map[string]interface{}{}
	if err != nil {
		for _, e := range err.(validator.ValidationErrors) {
			errTxt := e.Translate(trans)
			key := e.Field()

			//replace form key
			res := strings.ReplaceAll(errTxt, key, "`"+key+"`")
			resSlice := strings.Split(res," ")
			resfinal := ""
			for _, s := range resSlice {
				if !(strings.HasPrefix(s,"`") && strings.HasSuffix(s, "`")) {
					s = strings.ReplaceAll(s, "`", "")
				}
				resfinal += " " + s
			}
			errTxt = strings.TrimLeft(resfinal, " ")

			//set response of key
			errList[key] = errTxt
		}
	}

	return errList
}

func Struct(structData interface{}) map[string]interface{} {
	//validate.
	validate = validator.New()
	//v := New()

	trans := Translator(validate)
	/*
	//translation of validation
	var lang locales.Translator
	var trans ut.Translator
	var transFound bool

	switch Language {
	case "id":
		lang = id.New()
		uni := ut.New(lang, lang)
		trans, transFound = uni.GetTranslator(Language)
		if !transFound {
			log.Fatal("translator %s not found", Language)
		}
		id_translations.RegisterDefaultTranslations(validate, trans)
	case "en":
		lang = en.New()
		uni := ut.New(lang, lang)
		trans, transFound = uni.GetTranslator(Language)
		if !transFound {
			log.Fatal("translator %s not found", Language)
		}
		en_translations.RegisterDefaultTranslations(validate, trans)
	}
	*/

	// register function to get tag name from json tags.
	validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})

	//validation
	err := validate.Struct(structData)
	errList := map[string]interface{}{}
	if err != nil {
		for _, e := range err.(validator.ValidationErrors) {
			errTxt := e.Translate(trans)
			key := e.Field()

			//replace form key
			res := strings.ReplaceAll(errTxt, key, "`"+key+"`")
			resSlice := strings.Split(res," ")
			resfinal := ""
			for _, s := range resSlice {
				if !(strings.HasPrefix(s,"`") && strings.HasSuffix(s, "`")) {
					s = strings.ReplaceAll(s, "`", "")
				}
				resfinal += " " + s
			}
			errTxt = strings.TrimLeft(resfinal, " ")

			//set response of key
			errList[key] = errTxt
		}
	}

	return errList
}


func IsError(data interface{}) bool {
	switch v := data.(type) {
	case map[string]interface{}:
		return (len(v)>0)
	}


	fmt.Printf("cannot check error of %s.", data)
	return true
}

func IsJson(data interface{}) bool {
	var js map[string]interface{}

	switch v := data.(type) {
	case string:
		return json.Unmarshal([]byte(v), &js) == nil
	case []byte:
		return json.Unmarshal(v, &js) == nil
	default:
		fmt.Printf("failed converting '%v' to int, type is %v\n", data, reflect.TypeOf(data))
		return false
	}


	//https://stackoverflow.com/questions/22128282/how-to-check-string-is-in-json-format
	//var js string
	//return json.Unmarshal([]byte(s), &js) == nil
}


func TranslateError(err error, trans ut.Translator) (errs []error) {
	if err == nil {
		return nil
	}
	validatorErrs := err.(validator.ValidationErrors)
	for _, e := range validatorErrs {
		translatedErr := fmt.Errorf(e.Translate(trans))
		errs = append(errs, translatedErr)
	}
	return errs
}


