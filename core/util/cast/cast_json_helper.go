package cast

import (
	"encoding/json"
	"fmt"
	"reflect"
)

func JsonToStruct(data interface{}, structVariable interface{}) interface{} {
	var err error
	switch v := data.(type) {
	case []byte:
		err = json.Unmarshal(v, &structVariable)
		if err != nil {
			fmt.Printf("failed converting '%v' to struct, type is %v\n", data, reflect.TypeOf(data))
			return nil
		}
		return structVariable
	case string:
		err = json.Unmarshal([]byte(v), &structVariable)
		if err != nil {
			fmt.Printf("failed converting '%v' to struct, type is %v\n", data, reflect.TypeOf(data))
			return nil
		}
		return structVariable
	default:
		fmt.Printf("failed converting '%v' to struct, type is %v\n", data, reflect.TypeOf(data))
		return nil
	}
}

//https://stackoverflow.com/questions/11066946/partly-json-unmarshal-into-a-map-in-go
func JsonToMap(data interface{}) (map[string]interface{}, error) {
	var objmap map[string]interface{}
	var err error
	switch v := data.(type) {
	case error:
		s := v.Error()
		err = json.Unmarshal([]byte(s), &objmap)
		return objmap, err
	case string:
		err = json.Unmarshal([]byte(v), &objmap)
		return objmap, err
	}

	return objmap, err
}