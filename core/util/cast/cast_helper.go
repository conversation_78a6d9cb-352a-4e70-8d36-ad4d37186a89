package cast

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strconv"
)

func MapArrayToStruct(maps []map[string]interface{}, variable interface{}) error {
	structField := reflect.TypeOf(variable).Elem()
	structArray := reflect.ValueOf(variable).Elem()

	for _, m := range maps {
		newStruct := reflect.New(structField.Elem()).Elem()
		for k, v := range m {
			setField(newStruct, k, v)
		}
		structArray.Set(reflect.Append(structArray, newStruct))
	}

	return nil
}

func MapToStruct(m map[string]interface{}, s interface{}) error {
	for k, v := range m {
		setField(s, k, v)
	}
	return nil
}

func setField(m interface{}, key string, value interface{}) {
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("key %s, value %v, err : %v\n", key, value, r)
		}
	}()
	if value == nil {
		return
	}

	var structValue reflect.Value
	switch res := m.(type) {
	case reflect.Value:
		structValue = res
	default:
		structValue = reflect.ValueOf(m).Elem()
	}

	structFieldValue := structValue.FieldByName(key)

	//if key not match, search for json tag
	if !structFieldValue.IsValid() {
		for i := 0; i < structValue.NumField(); i++ {
			field := structValue.Type().Field(i)
			if v, ok := field.Tag.Lookup("json"); ok {
				if v == key {
					structFieldValue = structValue.FieldByName(field.Name)
					break
				}
			}
		}
	}

	if !structFieldValue.IsValid() {
		//fmt.Printf("no such field: %s in obj\n", key)
		return
	}

	if !structFieldValue.CanSet() {
		fmt.Printf("can not set %s field value\n", key)
		return
	}

	structFieldType := structFieldValue.Type()
	val := reflect.ValueOf(value)

	//if data type from struct and map different, convert it
	if structFieldType != val.Type() {
		switch structFieldType.Kind() {
		case reflect.String:
			val = reflect.ValueOf(ToString(value))
		case reflect.Int:
			val = reflect.ValueOf(ToInt(value))
		default:
			fmt.Printf("field %s type didn't match obj field type, type is %v while value is %v\n", key, structFieldType, val.Type())
			return
		}
	}

	structFieldValue.Set(val)
}

func ToString(data interface{}) string {
	switch v := data.(type) {
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case float32:
		return fmt.Sprintf("%f", v)
	case float64:
		return strconv.FormatFloat(v, 'f', 2, 64)
	case string:
		return v
	case []uint8:
		return string(v)
	case []interface{}:
		return fmt.Sprintf("%s", v[0])
	case error:
		return v.Error()
	case nil:
		return ""
	default:
		dataKind := reflect.ValueOf(data).Kind()
		if dataKind == reflect.Struct || dataKind == reflect.Map {
			dataJson, err := json.Marshal(data)
			if err == nil {
				return string(dataJson)
			}
		}
		fmt.Println("[ToString] - Invalid recognize data type toString. Type : ", dataKind, " | Data : ", data)
		return fmt.Sprintf("%s", data)
	}
}

func ToInt(data interface{}) int {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	result, err := strconv.Atoi(dataStr)
	if err != nil {
		switch i := data.(type) {
		case float32:
			return int(i)
		case float64:
			return int(i)
		default:
			fmt.Printf("failed converting '%v' to int, type is %v\n", data, reflect.TypeOf(data))
			return 0
		}
	} else {
		return result
	}
}

func ToInt64(data interface{}) int64 {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	result, err := strconv.ParseInt(dataStr, 10, 64)
	if err != nil {
		return 0
	} else {
		return result
	}
}

func ToError(data interface{}) error {
	switch v := data.(type) {
	case string:
		return errors.New(v)
	case map[string]interface{}:
		result, err := json.Marshal(v)
		if err != nil {
			fmt.Printf("failed converting '%v' to int, type is %v\n", data, reflect.TypeOf(data))
			return nil
		}
		return errors.New(string(result[:]))
	}
	return nil
}

func MapToError(data map[string]interface{}) (error, error) {
	hasil, err := json.Marshal(data)
	if err != nil {
		fmt.Println("error convert map to error")
		return nil, err
	}
	hasilString := string(hasil[:])
	result := errors.New(hasilString)
	return result, nil
}
