package cast

import (
	"testing"
	"uniqdev/api-pos-web/domain"
)

func TestToString(t *testing.T) {
	data := domain.User{Name: "jhon"}

	type args struct {
		data interface{}
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"test1", args{data}, `{"name":"jhon","email":"","phone":"","user_type":"","user_id":"","business_id":"","business_name":"","account_id":""}`},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ToString(tt.args.data); got != tt.want {
				t.Errorf("ToString() = %v, want %v", got, tt.want)
			}
		})
	}
}
