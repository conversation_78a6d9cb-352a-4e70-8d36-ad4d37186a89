package util

import (
	"crypto/md5"
	"encoding/hex"
	"golang.org/x/crypto/bcrypt"
)

func HashMd5(text string) string {
	hash := md5.Sum([]byte(text))
	return hex.EncodeToString(hash[:])
}

func BcryptHash(text string) (string, error) {
	// Hashing the password with the default cost of 10
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(text), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedPassword), nil
}

func BcryptVerify(text, bcryptHash string) error {
	err := bcrypt.CompareHashAndPassword([]byte(bcryptHash), []byte(text))
	if err != nil {
		return err
	}

	return nil
}