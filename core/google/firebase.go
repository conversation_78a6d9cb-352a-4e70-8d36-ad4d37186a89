package google

import (
	"context"
	"fmt"
	"log"
)

func VerifyIdToken(idToken string) (map[string]interface{}, error) {
	if firebaseApp == nil {
		return nil, fmt.Errorf("firebase auth not initialized")
	}

	ctx := context.Background()
	client, err := firebaseApp.Auth(ctx)
	if err != nil {
		return nil, err
	}

	token, err := client.VerifyIDToken(ctx, idToken)
	if err != nil {
		return nil, err
	}

	return token.Claims, nil
}

// CreateCustomToken uid: uid from firebase
func CreateCustomToken(uid string) (string, error) {
	if firebaseApp == nil {
		return "", fmt.Errorf("firebase auth not initialized")
	}

	ctx := context.Background()
	client, err := firebaseApp.Auth(ctx)
	if err != nil {
		log.Fatalf("error getting Auth client: %v\n", err)
		return "", err
	}

	token, err := client.CustomToken(ctx, uid)
	if err != nil {
		log.Fatalf("error minting custom token: %v\n", err)
		return "", err
	}

	log.Printf("Got custom token: %v\n", token)
	return token, nil
}
