package google

import (
	"context"
	"encoding/json"
	firebase "firebase.google.com/go/v4"
	"fmt"
	"github.com/joho/godotenv"
	"google.golang.org/api/option"
	"io/ioutil"
	"log"
	"os"
	log2 "uniqdev/api-pos-web/core/log"
)

var firebaseApp *firebase.App

func init() {
	err := godotenv.Load()
	if err != nil {
		fmt.Println("error loading .env file")
	}

	firebaseCredPath := os.Getenv("FIREBASE_CREDENTIAL")
	fmt.Println("FIREBASE_CREDENTIAL:", firebaseCredPath)
	projectId := ""

	authConfFile, err := ioutil.ReadFile(firebaseCredPath)
	if err == nil {
		var serviceAccount AuthService
		log2.IfError(json.Unmarshal(authConfFile, &serviceAccount))
		projectId = serviceAccount.ProjectId
	}

	opt := option.WithCredentialsFile(firebaseCredPath)
	config := &firebase.Config{ProjectID: projectId}
	firebaseApp, err = firebase.NewApp(context.Background(), config, opt)
	if err != nil {
		log.Fatalf("error initializing app: %v\n", err)
	}
}

type AuthService struct {
	Type                    string `json:"type"`
	ProjectId               string `json:"project_id"`
	PrivateKeyId            string `json:"private_key_id"`
	PrivateKey              string `json:"private_key"`
	ClientEmail             string `json:"client_email"`
	ClientId                string `json:"client_id"`
	AuthUri                 string `json:"auth_uri"`
	TokenUri                string `json:"token_uri"`
	AuthProviderX509CertUrl string `json:"auth_provider_x509_cert_url"`
	ClientX509CertUrl       string `json:"client_x509_cert_url"`
}
