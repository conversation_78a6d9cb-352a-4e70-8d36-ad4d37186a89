package apple_helper

import (
	"encoding/base64"
	"fmt"
	"os"
)

var (
	config = New()
)

type appleConfig struct {
	TeamId    string
	ClientId  string
	KeyId     string
	KeySecret string
}

func New() *appleConfig {
	keySecretBase64 := os.Getenv("APPLE_KEY_SECRET_BASE64")
	keySecret, err := base64.StdEncoding.DecodeString(keySecretBase64)
	if err != nil {
		fmt.Println("decoding APPLE_KEY_SECRET_BASE64 err: ", err)
	}

	return &appleConfig{
		TeamId:    os.Getenv("APPLE_TEAM_ID"),
		ClientId:  os.Getenv("APPLE_CLIENT_ID"),
		KeyId:     os.Getenv("APPLE_KEY_ID"),
		KeySecret: string(keySecret),
	}
}

func SetAppleConfig(teamId, clientId, keyId, keySecret string) {
	config = &appleConfig{
		TeamId:    teamId,
		ClientId:  clientId,
		KeyId:     keyId,
		KeySecret: keySecret,
	}
}

func GetAppleConfig() appleConfig {
	return *config
}
