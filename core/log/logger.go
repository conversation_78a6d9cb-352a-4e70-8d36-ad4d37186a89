package log

import (
	"fmt"
	"regexp"
	"runtime"
	"strings"
)

type logger struct {
	Config LoggerConfig
	hook   hook
}

type LoggerConfig struct {
	DisableColor bool
	HideTime     bool
	TimeOffset   int64
}

type logOutput struct {
	Message string
	Stacks  string
	Level   string
}

type hook interface {
	send(logOutput) error
}

var (
	sdt         = New()
	prefixError = "\u001B[1;31m[ERROR]\u001B[0m"
)

func New() *logger {
	return &logger{}
}

func SetLogger(config LoggerConfig) {
	sdt.setLogConfig(config)
}

func AddHook(h hook) {
	sdt.hook = h
}

func (l *logger) setLogConfig(config LoggerConfig) {
	l.Config = config
}

func IfError(err error) bool {
	if err != nil {
		stackSlice := make([]byte, 512)
		s := runtime.Stack(stackSlice, false)
		stacks := string(stackSlice[0:s])
		stacks = beautifyStacks(stacks)

		if sdt.hook != nil {
			errHook := sdt.hook.send(logOutput{
				Message: err.Error(),
				Stacks:  stacks,
				Level:   "Error",
			})
			if errHook != nil {

			}
		}
		fmt.Println(prefixError, err, "\n", stacks)
		return true
	}
	return false
}

func Info(msg string, v ...interface{}) {
	_, fn, line, _ := runtime.Caller(1)
	prefix := fmt.Sprintf("%s:%d  >> ", fn, line)
	fmt.Printf(prefix+msg+"\n", v...)
}

func beautifyStacks(stacks string) (result string) {
	defer func() {
		if r := recover(); r != nil {
			result = stacks
		}
	}()

	rex, _ := regexp.Compile(`[/a-zA-Z\d\-_/]+\.go:\d+\s`)
	callers := rex.FindAllString(stacks, -1)

	rex, _ = regexp.Compile(`([a-zA-Z]+\.{1,3})+[a-zA-Z]+((\([a-zA-Z0-9\s,\.]*\))|)\012`)
	functions := rex.FindAllString(stacks, -1)
	funcNames := make([]string, 0)
	for _, x := range functions {
		fName := strings.Replace(x, "\n", "", 1)
		param := regexp.MustCompile(`\(.+\)`).FindString(fName)
		if param != "" {
			fName = strings.Replace(fName, param, "()", 1)
		}
		funcNames = append(funcNames, fName)
	}

	for i, f := range callers {
		if i == 0 {
			continue
		}
		fName := ""
		if len(funcNames) > i {
			fName = funcNames[i]
		}
		result += fmt.Sprintf("\tat %s   %s\n", fName, strings.TrimSpace(f))
	}
	return result
}
