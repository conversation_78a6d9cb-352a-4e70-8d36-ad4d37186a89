package mysql

import (
	"database/sql"
	"fmt"
	"strings"
	"uniqdev/api-pos-web/core/util/cast"
)

// Repository struct
type Repository struct {
	Conn *sql.DB
}

// Query sql
func (db *Repository) Query(sql string, args ...interface{}) (result *SqlResult) {
	tableData := make([]map[string]interface{}, 0)
	result = &SqlResult{Data: tableData}

	result.SqlQuery = getSQLRaw(sql, args...)
	rows, err := db.Conn.Query(sql, args...)
	if err != nil {
		result.Error = err
		fmt.Println("Query Err: ", strings.Replace(result.SqlQuery, "\n", " ", -1), err)
		// log.IfError(fmt.Errorf("%v Query Err: `%s` %v", log.GetCaller("repository"), result.SqlQuery, err)) // log error
		return
	}

	defer func() {
		err := rows.Close()
		if err != nil {
			fmt.Println("closing sql row error")
			fmt.Println(err)
		}
	}()

	columns, err := rows.Columns()
	if err != nil {
		result.Error = err
		return
	}

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	for rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			result.Error = err
			return
		}

		entry := make(map[string]interface{})
		for i, col := range columns {
			v := values[i]
			if b, ok := v.([]byte); ok {
				entry[col] = string(b)
			} else {
				entry[col] = v
			}
		}
		tableData = append(tableData, entry)
	}

	result.Error = rows.Err()
	result.Data = tableData
	return
}

// Insert func
func (db *Repository) Insert(table string, data map[string]interface{}) (sql.Result, error) {
	values := make([]interface{}, 0)
	query := "INSERT INTO " + table + " ("
	for col, val := range data {
		query += col + ","
		values = append(values, val)
	}
	query = query[:len(query)-1] + ") VALUES (" + strings.Repeat("?, ", len(data)-1) + "?) "
	res, err := db.Conn.Exec(query, values...)
	return res, err
}

// BulkInsert insert bulk
func (db *Repository) BulkInsert(table string, data []map[string]interface{}) (sql.Result, error) {
	values := make([]interface{}, 0)
	query := "INSERT INTO " + table
	var key []string

	for _, t := range data {
		query += "("
		for u := range t {
			query += u + ", "
			key = append(key, u)
		}
		query = query[:len(query)-2]
		query += ") VALUES "
		break
	}

	for _, v := range data {
		query += "("
		for _, w := range key {
			values = append(values, v[w])
			query += "?, "
		}
		query = query[:len(query)-2]
		query += "), "
	}
	query = query[:len(query)-2]
	res, err := db.Conn.Exec(query, values...)
	return res, err
}

// Updates func
func (db *Repository) Updates(table string, data map[string]interface{}, where map[string]interface{}) (sql.Result, error) {
	wheres := make([]interface{}, 0)
	query := "UPDATE " + table + " SET "
	for col, val := range data {
		if val != nil {
			val2 := fmt.Sprintf("%v", val)
			val2 = strings.ReplaceAll(val2, "'", "\\'") //escape single quote
			query += col + "='" + val2 + "', "
		} else {
			query += col + "=" + fmt.Sprintf("NULL") + ", "
		}
	}
	query = query[:len(query)-2]
	query += " WHERE "
	if len(where) > 1 {
		for col, val := range where {
			query += col + "=? AND "
			wheres = append(wheres, val)
		}
		query = query[:len(query)-4]
	} else {
		for col, val := range where {
			query += col + "=?"
			wheres = append(wheres, val)
		}
	}
	res, err := db.Conn.Exec(query, wheres...)
	return res, err
}

// SingleUpdate single update query
func (db *Repository) SingleUpdate(table string, where string, data []map[string]interface{}) (sql.Result, error) {
	var key []string
	query := "UPDATE " + table
	fmt.Println(data)
	for _, a := range data {
		query += " SET "
		for b := range a {
			query += b + " = ?, "
			key = append(key, b)
		}
		query = query[:len(query)-2]
		break
	}

	query += " WHERE " + where + " = ?"

	val := []interface{}{}
	for _, v := range data {
		for _, w := range key {
			val = append(val, v[w])
		}
		val = append(val, v[where])
	}

	res, err := db.Conn.Exec(query, val...)
	return res, err
}

// BulkUpdate bulk update query
func (db *Repository) BulkUpdate(table string, where string, data []map[string]interface{}) error {
	var key []string
	query := "UPDATE " + table
	for _, a := range data {
		query += " SET "
		for b := range a {
			if b != where {
				query += b + " = ?, "
				key = append(key, b)
			}
		}
		query = query[:len(query)-2]
		break
	}

	query += " WHERE " + where + " = ?"

	arrval := [][]interface{}{}
	val := make([]interface{}, 0)
	for _, v := range data {
		for _, w := range key {
			val = append(val, v[w])
		}
		val = append(val, v[where])
		arrval = append(arrval, val)
		val = []interface{}{}
	}

	tx, err := db.Conn.Begin()
	if err != nil {
		fmt.Println(err)
		return err
	}

	stt, err := tx.Prepare(query)
	if err != nil {
		fmt.Println(err)
		return err
	}

	for _, k := range arrval {
		_, err := stt.Exec(k...)
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	tx.Commit()
	return nil
}

// Deletes func
func (db *Repository) Deletes(table string, where map[string]interface{}) (sql.Result, error) {
	wheres := make([]interface{}, 0)
	query := "DELETE FROM " + table
	if len(where) > 1 {
		query += " WHERE "
		for col, val := range where {
			query += fmt.Sprintf("%v", col) + "=? AND "
			wheres = append(wheres, val)
		}
		query = query[:len(query)-4]
	} else {
		for col, val := range where {
			query += " WHERE " + fmt.Sprintf("%v", col) + "=?"
			wheres = append(wheres, val)
		}
	}
	res, err := db.Conn.Exec(query, wheres...)
	return res, err
}

func getSQLRaw(sql string, params ...interface{}) string {
	for i := 0; i < len(params); i++ {
		index := strings.Index(sql, "?")
		sql = sql[:index] + cast.ToString(params[i]) + sql[index+1:]
	}
	return sql
}

// QueryWhereAnd where and
func (db *Repository) QueryWhereAnd(sql string, args []interface{}) ([]map[string]interface{}, error) {
	tableData := make([]map[string]interface{}, 0)
	rows, err := db.Conn.Query(sql, args...)
	if err != nil {
		return tableData, err
	}

	defer func() {
		err := rows.Close()
		if err != nil {
			fmt.Println("closing sql row error")
		}
	}()

	columns, err := rows.Columns()
	if err != nil {
		return tableData, err
	}

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	for rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			return tableData, err
		}

		entry := make(map[string]interface{})
		for i, col := range columns {
			v := values[i]

			b, ok := v.([]byte)
			if ok {
				entry[col] = string(b)
			} else {
				entry[col] = v
			}
		}
		tableData = append(tableData, entry)
	}

	err = rows.Err()
	if err != nil {
		return tableData, err
	}

	return tableData, nil
}
