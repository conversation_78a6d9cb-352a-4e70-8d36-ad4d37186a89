package mysql

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"
)

type Transaction interface {
	Insert(table string, data map[string]interface{}) sql.Result
	Update(table string, data map[string]interface{}, whereCond string, whereParams ...interface{}) sql.Result
}

type TxFn func(tx Transaction) error

type SqlTx struct {
	Tx *sql.Tx
}

func (s SqlTx) Insert(table string, data map[string]interface{}) sql.Result {
	values := make([]interface{}, 0)
	query := "INSERT INTO " + table + " ("
	for col, val := range data {
		query += col + ","
		values = append(values, val)
	}
	query = query[:len(query)-1] + ") VALUES (" + strings.Repeat("?, ", len(data)-1) + "?)"
	resp, err := s.Tx.Exec(query, values...)
	if err != nil {
		fmt.Printf("%v \nquery : %s", err, getSQLRaw(query, values...))
		panic(err)
	}
	return resp
}

func (s SqlTx) Update(table string, data map[string]interface{}, whereCond string, whereParams ...interface{}) sql.Result {
	values := make([]interface{}, 0)
	query := "UPDATE " + table + " SET "
	for col, val := range data {
		query += col + " = ?,"
		values = append(values, val)
	}
	query = query[:len(query)-1] + " WHERE " + whereCond

	for _, param := range whereParams {
		values = append(values, param)
	}

	res, err := s.Tx.Exec(query, values...)
	if err != nil {
		panic(fmt.Sprintf("%v \nquery : %s", err, getSQLRaw(query, values...)))
	}

	return res
}

func (db *Repository) WithTransaction(fn TxFn) (err error) {
	tx, err := db.Conn.Begin()
	if err != nil {
		return err
	}
	sqlTx := SqlTx{tx}

	defer func() {
		if p := recover(); p != nil {
			sqlTx.Tx.Rollback()
			err = errors.New(fmt.Sprintf("%v", p))
		} else if err != nil {
			// something went wrong, rollback
			sqlTx.Tx.Rollback()
		} else {
			// all good, commit
			sqlTx.Tx.Commit()
		}
	}()
	err = fn(sqlTx)
	return err
}
