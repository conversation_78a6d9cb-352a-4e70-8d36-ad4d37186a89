variables:
  GITLAB_IMAGE_NAME: ${CI_REGISTRY}/${CI_PROJECT_PATH}:latest-${CI_COMMIT_REF_NAME}

stages:
  - build
  - deploy

build_image:
  stage: build
  image: docker:26.1.1-alpine3.19
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: manual
    - if: '$CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_BRANCH == "staging"'
  services:
    - docker:26.1.1-dind-alpine3.19
  before_script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
  script:
    - docker build -t ${GITLAB_IMAGE_NAME} .
    - docker push ${GITLAB_IMAGE_NAME}
  after_script:
    - docker logout ${CI_REGISTRY}

deploy_dev:
  stage: deploy
  image: docker/compose:alpine-1.29.2
  environment:
    name: development
  only:
    - dev
  before_script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
    - cat $ENV > .env
  script:
    - mkdir -p /data/api-pos-web
    - cat $FIREBASE_CREDENTIAL > /data/api-pos-web/firebase_credential.json
    - docker pull ${GITLAB_IMAGE_NAME}
    - docker container rm -f ${CI_PROJECT_NAME} || true
    - docker run --name ${CI_PROJECT_NAME} -d --restart=unless-stopped --env-file=.env -v /docker/runner/data/api-pos-web/:/credential/ --network=uniq-network --log-driver=gcplogs --log-opt gcp-project=uniq-187911 $GITLAB_IMAGE_NAME
  after_script:
    - docker logout ${CI_REGISTRY}
  tags:
    - testing-docker

deploy_staging:
  stage: deploy
  image: docker/compose:alpine-1.29.2
  only:
    - staging
  environment:
    name: staging
  before_script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
    - cat ${ENV} > .env
    - mkdir -p /data/api-pos-web
    - cat $FIREBASE_CREDENTIAL > /data/api-pos-web/firebase_credential.json
  script:
    - docker pull ${GITLAB_IMAGE_NAME}
    - docker container rm -f ${CI_PROJECT_NAME} || true
    - docker run --name ${CI_PROJECT_NAME} -d --restart=unless-stopped --env-file=.env -v /docker/runner/data/api-pos-web/:/credential/ --network=uniq-network --log-driver=gcplogs --log-opt gcp-project=uniq-187911 $GITLAB_IMAGE_NAME
  after_script:
    - docker logout ${CI_REGISTRY}
  tags:
    - staging


deploy_production:
  stage: deploy
  image: docker/compose:alpine-1.29.2
  only:
    - master
    - tags
  environment:
    name: production
  before_script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
    - cat ${ENV} > .env
  script:
    - docker pull ${GITLAB_IMAGE_NAME}
    - docker container rm -f ${CI_PROJECT_NAME} || true
    - docker run --name ${CI_PROJECT_NAME} -d --restart=unless-stopped --env-file=.env --network=uniq-network --log-driver=gcplogs --log-opt gcp-project=uniq-187911 $GITLAB_IMAGE_NAME
  after_script:
    - docker logout ${CI_REGISTRY}
  tags:
    - production
