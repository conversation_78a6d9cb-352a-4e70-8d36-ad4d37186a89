package mysql

import (
	"database/sql"
	"uniqdev/api-pos-web/core/mysql"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/domain"
)

type mysqlLoginRepository struct {
	mysql.Repository
}

func NewMysqlLoginRepository(conn *sql.DB) domain.LoginRepository {
	return &mysqlLoginRepository{mysql.Repository{Conn: conn}}
}

func (r *mysqlLoginRepository) FindPassword(email string) (string, error) {
	result, err := r.Repository.Query("select password from admin where email = ? limit 1", email).Map()
	return cast.ToString(result["password"]), err
}

func (r *mysqlLoginRepository) FindUserByEmail(email string) (domain.User, error) {
	var user domain.User
	err := r.Query("select * from admin where email = ? limit 1", email).Model(&user)
	return user, err
}


