package http

import (
	"github.com/gofiber/fiber/v2"
	"uniqdev/api-pos-web/domain"
)

type loginHandler struct {
	domain.LoginUseCase
}

func NewLoginHandler(app *fiber.App, uc domain.LoginUseCase) {
	handler := &loginHandler{uc}

	app.Post("/sample/authorization", handler.Login)
}

func (h *loginHandler) Login(c *fiber.Ctx) error {
	user, err := h.LoginUseCase.CreateToken(domain.Login{
		Email:    c.FormValue("email"),
		Password: c.<PERSON>Value("password"),
	})

	if err != nil {
		return err
	}

	return c.<PERSON>(user)
}
