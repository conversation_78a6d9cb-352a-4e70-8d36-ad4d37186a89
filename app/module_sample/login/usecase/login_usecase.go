package usecase

import (
	"errors"
	"github.com/dgrijalva/jwt-go"
	"golang.org/x/crypto/bcrypt"
	"time"
	"uniqdev/api-pos-web/domain"
)

type loginUseCase struct {
	domain.LoginRepository
}

func NewLoginUseCase(repo domain.LoginRepository) domain.LoginUseCase {
	return &loginUseCase{repo}
}

func (u *loginUseCase) CreateToken(lg domain.Login) (domain.UserToken, error) {
	var userToken domain.UserToken

	//1.check user from db
	pass, err := u.LoginRepository.FindPassword(lg.Email)

	if pass == "" {
		return userToken, errors.New("invalid email or password")
	}

	//2. validate password...
	err = bcrypt.CompareHashAndPassword([]byte(pass), []byte(lg.Password))
	if err != nil {
		return userToken, errors.New("invalid email or password")
	}

	//get user detail
	user, err := u.FindUserByEmail(lg.Email)
	if err != nil {
		return userToken, err
	}

	//3. if valid, then create token
	token := jwt.New(jwt.SigningMethodHS256)
	expired := time.Now().Add(time.Hour * 72).Unix()
	claims := token.Claims.(jwt.MapClaims)
	claims["exp"] = expired
	claims["role"] = "{}"
	//claims["uid"] = user.UserId

	// Generate encoded token and send it as response.
	t, err := token.SignedString([]byte("secret"))

	userToken.User = user
	userToken.Token = domain.Token{
		Token:   t,
		Expired: expired,
		Type:    "Bearer",
	}

	return userToken, err
}
