package mysql

import (
	"database/sql"
	"fmt"
	"log"
	"uniqdev/api-pos-web/core/mysql"
	"uniqdev/api-pos-web/domain"
)

type mySqlProductRepository struct {
	mysql.Repository
}

func NewMySqlProductRepository(conn *sql.DB) domain.ProductRepository {
	return &mySqlProductRepository{mysql.Repository{Conn: conn}}
}

func (m *mySqlProductRepository) Fetch() ([]domain.Product, error) {
	var result []domain.Product
	err := m.Query("select * from products").Model(&result)
	if err != nil {
		log.Printf("getting product error: %v\n", err)
		return nil, err
	}
	return result, nil
}

func (m *mySqlProductRepository) FetchById(id int64) (domain.Product, error) {
	var result domain.Product
	err := m.Query("select * from products where product_id = ?", id).Model(&result)
	if err != nil {
		fmt.Println("get product error", err)
	}
	return result, nil
}

func (m *mySqlProductRepository) Add(product domain.Product) error {
	_, err := m.Insert("product", map[string]interface{}{
		"name":       product.Name,
		"product_id": product.ProductId,
	})
	return err
}
