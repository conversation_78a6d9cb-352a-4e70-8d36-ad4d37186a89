package mysql_test

import (
	"github.com/DATA-DOG/go-sqlmock"
	"testing"
	"uniqdev/api-pos-web/app/module_sample/product/repository/mysql"
	"uniqdev/api-pos-web/domain"
)

func TestAdd(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer db.Close()

	product := domain.Product{
		ProductId: 1,
		Name:      "Xiaomi 6X",
	}
	mock.ExpectExec("INSERT INTO product").WithArgs("Xiaomi 6X", 1).WillReturnResult(sqlmock.NewResult(1, 1))

	repo := mysql.NewMySqlProductRepository(db)
	if err := repo.Add(product); err != nil {
		t.Errorf("expecting no error: %v", err)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.<PERSON>("there were unfulfilled expectations: %s", err)
	}
}
