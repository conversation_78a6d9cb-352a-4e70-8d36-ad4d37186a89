package grpc

import (
	"context"
	"google.golang.org/grpc"
	"log"
	pb "uniqdev/api-pos-web/app/module_sample/product/delivery/grpc/pb"
	"uniqdev/api-pos-web/domain"
)

type ProductHandler struct {
	pb.UnimplementedProductServiceServer
	ProductUseCase domain.ProductUseCase
}

func NewProductHandler(s *grpc.Server, uc domain.ProductUseCase) {
	pb.RegisterProductServiceServer(s, &ProductHandler{ProductUseCase: uc})
}

func (p *ProductHandler) GetProduct(ctx context.Context, in *pb.Int64Value) (*pb.Product, error) {
	product, err := p.ProductUseCase.FetchById(in.Value)
	if err != nil {
		log.Fatal(err)
	}
	return &pb.Product{ProductId: int32(product.ProductId), Name: product.Name, Barcode: product.Barcode}, nil
}
