package main

import (
	"encoding/json"
	"fmt"
	"github.com/dgrijalva/jwt-go"
	"github.com/gofiber/fiber/v2"
	jwtware "github.com/gofiber/jwt/v2"
	"github.com/joho/godotenv"
	"google.golang.org/grpc"
	"net"
	"os"
	"os/signal"
	"syscall"
	"uniqdev/api-pos-web/app/config"
	loginHttp "uniqdev/api-pos-web/app/module_sample/login/delivery/http"
	loginRepoMysql "uniqdev/api-pos-web/app/module_sample/login/repository/mysql"
	loginUseCase "uniqdev/api-pos-web/app/module_sample/login/usecase"
	productGrpc "uniqdev/api-pos-web/app/module_sample/product/delivery/grpc"
	productHttp "uniqdev/api-pos-web/app/module_sample/product/delivery/http"
	"uniqdev/api-pos-web/app/module_sample/product/repository/gorm"
	"uniqdev/api-pos-web/app/module_sample/product/usecase"
	"uniqdev/api-pos-web/core/log"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/token"
	"uniqdev/api-pos-web/core/util/validation"
	"uniqdev/api-pos-web/domain"
	authHttp "uniqdev/api-pos-web/module/auth/delivery/http"
	authRepoMysql "uniqdev/api-pos-web/module/auth/repository/mysql"
	authUseCase "uniqdev/api-pos-web/module/auth/usecase"
)

func init() {
	err := godotenv.Load()
	if err != nil {
		fmt.Println("error loading .env file")
	}
	log.AddHook(&log.SlackHook{
		HookUrl: os.Getenv("SLACK_HOOK_URL"),
		Channel: os.Getenv("SLACK_CHANNEL"),
	})
}

func main() {
	//get connetion
	gormConn := config.GetGormConn()
	dbConn := config.GetMySqlConn()
	defer config.CloseMySqlConn(dbConn)

	//load usecase (sample)
	loginUseCase := loginUseCase.NewLoginUseCase(loginRepoMysql.NewMysqlLoginRepository(dbConn))
	productUseCase := usecase.NewProductUseCase(gorm.NewGormProductRepository(gormConn)) //use gorm
	//productUseCase := usecase.NewProductUseCase(mysql.NewMySqlProductRepository(dbConn)) //use native


	//load usecase
	authUseCase := authUseCase.AuthUseCase(authRepoMysql.AuthRepository(dbConn))

	//http ------
	app := fiber.New()
	app.Static("/", "./app/static")
	app.Get("/", func(c *fiber.Ctx) error {
		return c.SendString("hello world")
	})

	// Get User Language by Header
	app.Use(func(c *fiber.Ctx) error {
		allowLang := c.AcceptsLanguages("id","en")
		lang := cast.ToString(c.Request().Header.Peek("Accept-Language"))
		if allowLang != "" {
			if lang != "" {
				fmt.Printf("load lang %s\n", lang)
				validation.Language = lang
			}
		}else{
			fmt.Printf("lang %s not found\n", lang)
		}

		return c.Next()
	})

	loginHttp.NewLoginHandler(app, loginUseCase)
	authHttp.AuthHandler(app, authUseCase)

	//http JWT middleware, all route after this will use JWT authentication
	app.Use(jwtware.New(jwtware.Config{
		SigningKey: []byte("secret"),
		SuccessHandler: func(c *fiber.Ctx) error {
			//get token data
			tokenData := c.Locals("user").(*jwt.Token)
			claims := tokenData.Claims.(jwt.MapClaims)
			jsonString, _ := json.Marshal(claims["data"])

			//share token data
			var user domain.User
			cast.JsonToStruct(jsonString, &user)
			token.UserData = user

			return c.Next()
		},
	}))

	//define other route
	productHttp.NewProductHandler(app, productUseCase) //it just sample

	defer fmt.Println("service ended...")
	errs := make(chan error)

	go func() {
		c := make(chan os.Signal, 1)
		signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
		errs <- fmt.Errorf("%s", <-c)
	}()

	//grpc -------
	lis, err := net.Listen("tcp", ":3001")
	if err != nil {
		panic(err)
	}
	s := grpc.NewServer()
	productGrpc.NewProductHandler(s, productUseCase) //it just sample
	go func() {
		errs <- s.Serve(lis)
	}()

	go func() {
		port := "3000"
		if os.Getenv("PORT") != "" {
			port = os.Getenv("PORT")
		}
		errs <- app.Listen(":"+port)
	}()

	fmt.Printf("exit: %v", <-errs)
}
