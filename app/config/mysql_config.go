package config

import (
	"database/sql"
	"fmt"
	_ "github.com/go-sql-driver/mysql"
	"log"
	"os"
)

func GetMySqlConn() *sql.DB {
	username := os.Getenv("DB_USERNAME")
	password := os.Getenv("DB_PASSWORD")
	host := os.Getenv("DB_HOST")
	db := os.Getenv("DB_NAME")
	port := os.Getenv("DB_PORT")

	conn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s", username, password, host, port, db)
	dbConn, err := sql.Open("mysql", conn)

	if err != nil {
		fmt.Printf("failed to connect using config: %s:@tcp(%s:%s)/%s", username, host, port, db)
		log.Fatal(err)
	}

	err = dbConn.Ping()
	if err != nil {
		log.Fatal(err)
	}
	return dbConn
}

func CloseMySqlConn(dbConn *sql.DB) {
	err := dbConn.Close()
	if err != nil {
		log.Fatal(err)
	}
}
