package config

import (
	"fmt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"os"
)

func GetGormConn() *gorm.DB {
	username := os.Getenv("DB_USERNAME")
	password := os.Getenv("DB_PASSWORD")
	host := os.Getenv("DB_HOST")
	db := os.Getenv("DB_NAME")
	port := os.Getenv("DB_PORT")

	conn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s", username, password, host, port, db)
	dbConn, err := gorm.Open(mysql.Open(conn), &gorm.Config{
		SkipDefaultTransaction:true,
	})

	if err != nil {
		fmt.Printf("failed to connect using config: %s:@tcp(%s:%s)/%s", username, host, port, db)
		panic("gorm failed to create connection")
	}

	dbx, err := dbConn.DB()
	if err != nil {
		panic("gorm failed to get generic database")
	}

	//defer func() {
	//	if dbx.Close() != nil {
	//		fmt.Println("gorm failed to close connection to generic db")
	//	}
	//}()

	if dbx.Ping() != nil {
		panic("gorm failed to ping to database")
	}

	return dbConn
}
