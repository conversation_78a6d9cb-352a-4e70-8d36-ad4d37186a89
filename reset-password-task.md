# Reset Password Feature Implementation

Your task is to add a complete "reset password" feature to our Golang backend API.

## Endpoint
`POST /v1/auth_krakend/reset-password`

## Complete Flow Overview

### Phase 1: Email Request
**Client Request:**
```json
{
  "email": "<EMAIL>"
}
```

**Backend Process:**
1. Check if email exists in `accounts` table
2. If not found → return error "Email not registered"
3. If found → Generate 6-digit OTP code and verification token
4. Save hashed token to `auth_otp` table
5. Queue email with OTP code to `scheduled_message` table
6. Return verification token to client

**Response:**
```json
{
  "success": true,
  "message": "Verification code sent to email",
  "verification_token": "encrypted_token_here"
}
```

### Phase 2: Code Verification
**Client Request:**
```json
{
  "verification_token": "encrypted_token_here",
  "otp_code": "123456"
}
```

**Backend Process:**
1. Decrypt and validate verification token
2. Find corresponding OTP record in `auth_otp` table
3. Check if OTP code matches and hasn't expired
4. If valid → Generate reset password token
5. Save to `users_session` table with expiry (15 minutes)
6. Return reset token to client

**Response:**
```json
{
  "success": true,
  "message": "Code verified successfully",
  "reset_token": "encrypted_reset_token_here"
}
```

### Phase 3: Password Update
**Client Request:**
```json
{
  "reset_token": "encrypted_reset_token_here",
  "new_password": "newSecurePassword123!",
  "confirm_password": "newSecurePassword123!"
}
```

**Backend Process:**
1. Decrypt and validate reset token
2. Find session in `users_session` table
3. Check if token hasn't expired
4. Validate password requirements
5. Update password in `accounts` table (hash with bcrypt)
6. Invalidate/delete the session token
7. Return success response

## Database Schema Reference

### Accounts Table
```sql
CREATE TABLE `accounts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(30) NOT NULL,
  `phone` varchar(15) DEFAULT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(65) DEFAULT NULL,
  `created_at` bigint NOT NULL DEFAULT '0',
  `updated_at` bigint NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
```

### OTP Authentication Table
```sql
CREATE TABLE `auth_otp` (
  `auth_otp_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `contact` varchar(50) NOT NULL,
  `contact_type` enum('whatsapp','email','sms') NOT NULL,
  `token` varchar(255) NOT NULL,
  `otp_code` varchar(10) NOT NULL,
  `authenticated_at` bigint unsigned DEFAULT NULL,
  `date_created` bigint NOT NULL,
  `date_expired` bigint NOT NULL,
  PRIMARY KEY (`auth_otp_id`),
  UNIQUE KEY `auth_otp_token_idx` (`token`) USING BTREE
);
```

### Session Management Table
```sql
CREATE TABLE `users_session` (
  `id` varchar(128) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `timestamp` int unsigned NOT NULL DEFAULT '0',
  `data` blob,
  `token` varchar(150) DEFAULT NULL,
  `created_at` int unsigned NOT NULL DEFAULT '0',
  `expired_at` int unsigned DEFAULT NULL,
  `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `email` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ci_sessions_timestamp` (`timestamp`),
  KEY `token_idx` (`token`)
);
```

### Email Queue Table
```sql
CREATE TABLE `scheduled_message` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `identifier_id` varchar(50) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `message` mediumtext,
  `time_deliver` bigint NOT NULL,
  `time_sent` bigint DEFAULT '0',
  `data_created` bigint NOT NULL,
  `media` enum('email','whatsapp','sms','push_notif') NOT NULL,
  `receiver` mediumtext NOT NULL,
  `sent_via` varchar(50) DEFAULT 'EMAIL_SYSTEM',
  `status` enum('pending','sent','failed') NOT NULL DEFAULT 'pending',
  `error_reason` text,
  PRIMARY KEY (`id`)
);
```

## Token Management Strategy

### Verification Token (Phase 1 → Phase 2)
- **Purpose:** Link email request to OTP verification
- **Format:** `base64(encrypt(auth_otp_id + ":" + timestamp))`
- **Storage:** Hashed version in `auth_otp.token`
- **Expiry:** 10 minutes
- **Usage:** One-time use, invalidated after successful verification

### Reset Token (Phase 2 → Phase 3)
- **Purpose:** Allow password reset after OTP verification
- **Format:** `base64(encrypt(session_id + ":" + timestamp))`
- **Storage:** Hashed version in `users_session.token`
- **Expiry:** 15 minutes
- **Usage:** One-time use, invalidated after password update

## Security Requirements

### Password Validation
- Minimum 8 characters
- At least 1 uppercase letter
- At least 1 lowercase letter
- At least 1 number
- At least 1 special character

### Rate Limiting
- Max 3 email requests per email per hour
- Max 5 OTP verification attempts per token
- Max 3 password reset attempts per token

### Token Security
- Use AES-256-GCM for encryption
- Include timestamp for expiry validation
- Hash tokens before database storage using SHA-256
- Generate cryptographically secure random values

## Error Responses

### Standard Error Format
```json
{
  "success": false,
  "error_code": "ERROR_CODE",
  "message": "Human readable message",
  "details": "Additional details if needed"
}
```

### Error Codes to Implement
- `EMAIL_NOT_FOUND`: Email not registered
- `EMAIL_RATE_LIMITED`: Too many requests for this email
- `INVALID_TOKEN`: Token is invalid or malformed
- `TOKEN_EXPIRED`: Token has expired
- `INVALID_OTP`: OTP code is incorrect
- `OTP_EXPIRED`: OTP code has expired
- `TOO_MANY_ATTEMPTS`: Maximum verification attempts exceeded
- `PASSWORD_TOO_WEAK`: Password doesn't meet requirements
- `PASSWORDS_DONT_MATCH`: New password and confirmation don't match
- `INTERNAL_ERROR`: Server error occurred

## Email Template

### OTP Email Content
```
Subject: Reset Your Password - Verification Code

Hi there,

You requested to reset your password. Use this verification code:

**123456**

This code will expire in 10 minutes.

If you didn't request this, please ignore this email.

Thanks,
The Team
```

## Implementation Notes

1. **Encryption Key**: Use environment variable `RESET_PASSWORD_ENCRYPTION_KEY` for token encryption
2. **OTP Generation**: Use crypto/rand for secure 6-digit code generation
3. **Database Transactions**: Use transactions for multi-table operations
4. **Logging**: Log all password reset attempts with email and IP address
5. **Cleanup**: Implement cleanup job for expired tokens and sessions
6. **Testing**: Include unit tests for each phase and error scenarios

## Additional Considerations

- Add IP address logging for security audit
- Consider implementing CAPTCHA for repeated failed attempts
- Add optional SMS fallback for OTP delivery
- Implement account lockout after multiple failed reset attempts
- Consider adding email notification when password is successfully changed

Please implement this feature with proper error handling, logging, and security measures. Use existing project patterns and coding standards.