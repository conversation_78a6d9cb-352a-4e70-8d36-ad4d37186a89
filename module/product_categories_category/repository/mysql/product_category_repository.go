package mysql

import (
	"database/sql"
	"fmt"
	mysql "uniqdev/api-pos-web/core/mysql"
	"uniqdev/api-pos-web/core/util"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/token"
	domain "uniqdev/api-pos-web/domain"
)

type productCategoryRepository struct {
	mysql.Repository
}

func ProductCategoryRepository(db *sql.DB) domain.ProductCategoryRepository {
	return &productCategoryRepository{mysql.Repository{Conn: db}}
}

func (r productCategoryRepository) selectQuery() string {
	return "SELECT product_category_id AS id, name, code FROM products_category "
}

func (r productCategoryRepository) FetchById(id string) (domain.ProductCategory, error) {
	user := token.UserData

	var result domain.ProductCategory
	err := r.Query(
		r.selectQuery()+` WHERE product_category_id=? AND data_status=? AND admin_fkid=? AND data_type=?`, id, "on", user.BusinessId, "product").Model(&result)
	if err != nil {
		fmt.Println("error fetch product_category by id: ", err)
	}

	return result, err
}

func (r productCategoryRepository) FetchByCode(code string) (domain.ProductCategory, error) {
	user := token.UserData

	var result domain.ProductCategory
	err := r.Query(
		r.selectQuery()+` WHERE code=? AND data_status=? AND admin_fkid=? AND data_type=?`, code, "on", user.BusinessId, "product").Model(&result)
	if err != nil {
		fmt.Println("error fetch product_category by code: ", err)
	}

	return result, err
}

func (r productCategoryRepository) Add(input domain.ProductCategoryInput) (domain.ProductCategory, error) {
	user := token.UserData
	timeMillis := util.CurrentMillis()

	//lakukan insert
	insert := map[string]interface{}{
		"name": input.Name,
		"admin_fkid": user.BusinessId,
		"data_created": timeMillis,
		"data_modified": timeMillis,
		"data_status": "on",
	}
	if input.Code != "" {
		insert["code"] = input.Code
	}
	result, err := r.Insert("products_category", insert)
	if err != nil {
		fmt.Println("error create product category: ", err)
		return domain.ProductCategory{}, err
	}

	id, _ := result.LastInsertId()
	return domain.ProductCategory{
		ID:   cast.ToString(id),
		Name: input.Name,
		Code: input.Code,
	}, err
}

func (r productCategoryRepository) Update(input domain.ProductCategory) (domain.ProductCategory, error) {
	user := token.UserData
	timeMillis := util.CurrentMillis()

	//lakukan update
	data := map[string]interface{}{
		"name": input.Name,
		"code": nil,
		"data_modified": timeMillis,
	}
	if input.Code != "" {
		data["code"] = input.Code
	}
	_, err := r.Updates("products_category", data, map[string]interface{}{
		"product_category_id": input.ID,
		"admin_fkid": user.BusinessId,
		"data_status": "on",
		"data_type": "product",
	})
	if err != nil {
		fmt.Println("error update product category: ", err)
	}
	//fmt.Println(result)
	return domain.ProductCategory{
		ID:   input.ID,
		Name: input.Name,
		Code: input.Code,
	}, err
}

func (r productCategoryRepository) DeleteById(id string) error {
	user := token.UserData

	_, err := r.Deletes("products_category", map[string]interface{}{
		"product_category_id": id,
		"admin_fkid": user.BusinessId,
		"data_status": "on",
		"data_type": "product",
	})
	if err != nil {
		fmt.Println("error delete product category: ",err)
	}
	return err
}
