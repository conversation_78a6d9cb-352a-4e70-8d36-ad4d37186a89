package usecase

import (
	"encoding/json"
	"github.com/go-playground/validator/v10"
	"regexp"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/validation"
	domain "uniqdev/api-pos-web/domain"
)

type productCategoryUseCase struct {
	repo domain.ProductCategoryRepository
}

func ProductCategoryUseCase(repository domain.ProductCategoryRepository) domain.ProductCategoryUseCase {
	return &productCategoryUseCase{repository}
}

func (u productCategoryUseCase) FetchById(id string) (domain.ProductCategory, error) {
	return u.repo.FetchById(id)
}

func (u productCategoryUseCase) Add(input domain.ProductCategoryInput) (domain.ProductCategory, error) {
	var (
		result domain.ProductCategory
		err error
	)

	v, trans := validation.New()
	//custom validation
	v.RegisterValidation("is_unique", func(fl validator.FieldLevel) bool {
		data := fl.Field().String()
		if data != "" {
			res, _ := u.repo.FetchByCode(data)
			return res.ID == ""
		}
		return true
	})
	v.RegisterValidation("alphanum", func(fl validator.FieldLevel) bool {
		data := fl.Field().String()
		if data != "" {
			result, _ := regexp.Match("^[a-zA-Z\\d-]+$", []byte(data))
			return result
		}
		return true
	})
	errList := validation.Lang{
		ID: "{0} telah digunakan",
		EN: "{0} already used",
		Trans: trans,
	}.RegisterTranslation(v, "is_unique", false).Struct(v, input)
	if validation.IsError(errList) {
		err, _ = cast.MapToError(errList)
		return result, err
	}

	result, err = u.repo.Add(input)
	return result, err
}

func (u productCategoryUseCase) Update(input domain.ProductCategory) (domain.ProductCategory, error) {
	var result domain.ProductCategory

	//check id is valid
	rst, _ := u.repo.FetchById(input.ID)
	if rst.ID == "" {
		return result, cast.ToError("404")
	}

	//validation
	b, _ := json.Marshal(input)
	js := cast.ToString(b)
	var inputValidation domain.ProductCategoryInput
	cast.JsonToStruct(js, &inputValidation)

	var errList map[string]interface{}
	v, trans := validation.New()
	v.RegisterValidation("is_unique", func(fl validator.FieldLevel) bool {
		data := fl.Field().String()
		if data != "" {
			res, _ := u.repo.FetchByCode(data)
			return res.ID == input.ID || res.ID == ""
		}
		return true
	})
	v.RegisterValidation("alphanum", func(fl validator.FieldLevel) bool {
		data := fl.Field().String()
		if data != "" {
			result, _ := regexp.Match("^[a-zA-Z\\d-]+$", []byte(data))
			return result
		}
		return true
	})
	errList = validation.Lang{
		ID: "{0} telah digunakan",
		EN: "{0} already used",
		Trans: trans,
	}.RegisterTranslation(v, "is_unique", false).Struct(v, inputValidation)
	if validation.IsError(errList) {
		res, _ := cast.MapToError(errList)
		return result, res
	}

	//process update
	rs, err := u.repo.Update(input)
	return rs, err
}

func (u productCategoryUseCase) DeleteById(id string) error {
	//check id is valid
	result, err := u.repo.FetchById(id)
	if err != nil {
		return err
	}
	if result.ID == "" {
		return cast.ToError("404")
	}

	//delete process
	err = u.repo.DeleteById(id)
	return err
}
