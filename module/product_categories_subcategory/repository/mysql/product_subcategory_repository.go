package mysql

import (
	"database/sql"
	"fmt"
	mysql "uniqdev/api-pos-web/core/mysql"
	"uniqdev/api-pos-web/core/util"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/token"
	domain "uniqdev/api-pos-web/domain"
)

type productSubcategoryRepository struct {
	mysql.Repository
}

func ProductSubcategoryRepository(db *sql.DB) domain.ProductSubcategoryRepository {
	return &productSubcategoryRepository{mysql.Repository{Conn: db}}
}

func (r productSubcategoryRepository) selectQuery() string {
	return "SELECT product_subcategory_id AS id, name, code FROM products_subcategory "
}

func (r productSubcategoryRepository) FetchById(id string) (domain.ProductSubcategory, error) {
	user := token.UserData

	var result domain.ProductSubcategory
	err := r.Query(
		r.selectQuery()+` WHERE product_subcategory_id=? AND data_status=? AND admin_fkid=? AND data_type=?`, id, "on", user.BusinessId, "product").Model(&result)
	if err != nil {
		fmt.Println("error fetch product_subcategory by id: ", err)
	}

	return result, err
}

func (r productSubcategoryRepository) Add(input domain.ProductSubcategoryInput) (domain.ProductSubcategory, error) {
	user := token.UserData
	timeMillis := util.CurrentMillis()

	//lakukan insert
	insert := map[string]interface{}{
		"name": input.Name,
		"admin_fkid": user.BusinessId,
		"data_created": timeMillis,
		"data_modified": timeMillis,
		"data_status": "on",
	}
	if input.Code != "" {
		insert["code"] = input.Code
	}
	result, err := r.Insert("products_subcategory", insert)
	if err != nil {
		fmt.Println("error create product subcategory: ", err)
		return domain.ProductSubcategory{}, err
	}

	id, _ := result.LastInsertId()
	return domain.ProductSubcategory{
		ID:   cast.ToString(id),
		Name: input.Name,
		Code: input.Code,
	}, err
}

func (r productSubcategoryRepository) Update(input domain.ProductSubcategory) (domain.ProductSubcategory, error) {
	user := token.UserData
	timeMillis := util.CurrentMillis()

	//lakukan update
	data := map[string]interface{}{
		"name": input.Name,
		"code": nil,
		"data_modified": timeMillis,
	}
	if input.Code != "" {
		data["code"] = input.Code
	}
	_, err := r.Updates("products_subcategory", data, map[string]interface{}{
		"product_subcategory_id": input.ID,
		"admin_fkid": user.BusinessId,
		"data_status": "on",
		"data_type": "product",
	})
	if err != nil {
		fmt.Printf("error update product subcategory: %v\n", err)
	}
	//fmt.Println(result)
	return domain.ProductSubcategory{
		ID:   input.ID,
		Name: input.Name,
		Code: input.Code,
	}, err
}

func (r productSubcategoryRepository) DeleteById(id string) error {
	user := token.UserData

	_, err := r.Deletes("products_subcategory", map[string]interface{}{
		"product_subcategory_id": id,
		"admin_fkid": user.BusinessId,
		"data_status": "on",
		"data_type": "product",
	})
	if err != nil {
		fmt.Printf("error delete product_subcategory: %v \n:: %v \n", id, err)
	}
	return err
}

func (r productSubcategoryRepository) FetchByCode(code string) (domain.ProductSubcategory, error) {
	user := token.UserData

	var result domain.ProductSubcategory
	err := r.Query(
		r.selectQuery()+` WHERE code=? AND data_status=? AND admin_fkid=? AND data_type=?`, code, "on", user.BusinessId, "product").Model(&result)
	if err != nil {
		fmt.Println("error fetch product_subcategory by code: ", err)
	}

	return result, err
}
