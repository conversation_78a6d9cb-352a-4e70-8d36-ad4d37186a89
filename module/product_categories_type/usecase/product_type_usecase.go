package usecase

import (
	"encoding/json"
	"github.com/go-playground/validator/v10"
	"regexp"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/validation"
	domain "uniqdev/api-pos-web/domain"
)

type productTypeUseCase struct {
	repo domain.ProductTypeRepository
}

func ProductTypeUseCase(repository domain.ProductTypeRepository) domain.ProductTypeUseCase {
	return &productTypeUseCase{repository}
}

func (u productTypeUseCase) FetchById(id string) (domain.ProductType, error) {
	return u.repo.FetchById(id)
}

func (u productTypeUseCase) Add(input domain.ProductTypeInput) (domain.ProductType, error) {
	/*
	//run validation
	err := v.Struct(input)
	errs := validation.TranslateError(err, trans)
	fmt.Println(errs)

	fmt.Println("isi code: ",input.Code)
	if input.Code != "" {
		result, err := u.FetchByCode(input.Code)
		if result.ID != "" || err != nil{
			fmt.Println("data sudah ada")
			return result, err
		}
	}
	*/

	var (
		result domain.ProductType
		err error
	)

	v, tranx := validation.New()
	//custom validation
	v.RegisterValidation("is_unique", func(fl validator.FieldLevel) bool {
		data := fl.Field().String()
		if data != "" {
			res, _ := u.repo.FetchByCode(data)
			return res.ID == ""
		}
		return true
	})
	v.RegisterValidation("alphanum", func(fl validator.FieldLevel) bool {
		data := fl.Field().String()
		if data != "" {
			result, _ := regexp.Match("^[a-zA-Z\\d-]+$", []byte(data))
			return result
		}
		return true
	})
	errList := validation.Lang{
		ID: "{0} telah digunakan",
		EN: "{0} already used",
		Trans: tranx,
	}.RegisterTranslation(v, "is_unique", false).Struct(v, input)
	if validation.IsError(errList) {
		err, _ = cast.MapToError(errList)
		return result, err
	}

	result, err = u.repo.Add(input)
	return result, err
}

func (u productTypeUseCase) Update(input domain.ProductType) (domain.ProductType, error) {
	var result domain.ProductType

	//check is id valid
	rst, _ := u.repo.FetchById(input.ID)
	if rst.ID == "" {
		return result, cast.ToError("404")
	}

	//validation
	b, _ := json.Marshal(input)
	js := cast.ToString(b)
	var inputValidation domain.ProductTypeInput
	cast.JsonToStruct(js, &inputValidation)

	v, trans := validation.New()
	v.RegisterValidation("is_unique", func(fl validator.FieldLevel) bool {
		data := fl.Field().String()
		if data != "" {
			res, _ := u.repo.FetchByCode(data)
			return res.ID == input.ID || res.ID == ""
		}
		return true
	})
	v.RegisterValidation("alphanum", func(fl validator.FieldLevel) bool {
		data := fl.Field().String()
		if data != "" {
			result, _ := regexp.Match("^[a-zA-Z\\d-]+$", []byte(data))
			return result
		}
		return true
	})
	errList := validation.Lang{
		ID: "{0} telah digunakan",
		EN: "{0} already used",
		Trans: trans,
	}.RegisterTranslation(v, "is_unique", false).Struct(v, inputValidation)
	if validation.IsError(errList) {
		res, _ := cast.MapToError(errList)
		return result, res
	}

	//process update
	rs, err := u.repo.Update(input)



	return rs, err
}

func (u productTypeUseCase) DeleteById(id string) error {
	//check id is valid
	result, err := u.repo.FetchById(id)
	if err != nil {
		return err
	}
	if result.ID == "" {
		return cast.ToError("404")
	}

	//delete process
	err = u.repo.DeleteById(id)
	return err
}

func (u productTypeUseCase) sampleValidation()  {
	/*
	//validation v1
	v := validator.New()
	//translate validation
	lang := id.New()
	uni := ut.New(lang, lang)
	trans, _ := uni.GetTranslator("id")
	cek := id_translations.RegisterDefaultTranslations(v, trans)
	fmt.Println("error from cek: ", cek)
	v.RegisterTranslation("is_unique", trans, func(ut ut.Translator) error {
		return ut.Add("is_unique", "{0} already exist", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("is_unique", fe.Field())
		return t
	})

	//custom validation
	v.RegisterValidation("is_unique", func(fl validator.FieldLevel) bool {
		data := fl.Field().String()
		res, _ := u.FetchByCode(data)
		return res.ID == ""
	})
	*/
}