package mysql

import (
	"database/sql"
	"fmt"
	mysql "uniqdev/api-pos-web/core/mysql"
	"uniqdev/api-pos-web/core/util"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/token"
	domain "uniqdev/api-pos-web/domain"
)

type productTypeRepository struct {
	mysql.Repository
}

func ProductTypeRepository(db *sql.DB) domain.ProductTypeRepository {
	return &productTypeRepository{mysql.Repository{Conn: db}}
}

func (r productTypeRepository) FetchById(id string) (domain.ProductType, error) {
	user := token.UserData

	var result domain.ProductType
	err := r.Query(`
		SELECT products_type_id AS id, name, code 
		FROM products_type
		WHERE products_type_id=? AND data_status=? AND admin_fkid=?`, id, "on", user.BusinessId).Model(&result)
	if err != nil {
		fmt.Println("error fetch product_categories_type by id")
	}

	return result, err
}

func (r productTypeRepository) FetchByCode(code string) (domain.ProductType, error) {
	user := token.UserData

	var result domain.ProductType
	err := r.Query(`
		SELECT products_type_id AS id, name, code
		FROM products_type
		WHERE code=? AND data_status=? AND admin_fkid=?`, code, "on", user.BusinessId).Model(&result)
	fmt.Println(result)
	if err != nil {
		fmt.Println("error fetch product_categories_type by code: ", err)
	}

	return result, err
}

func (r productTypeRepository) Add(input domain.ProductTypeInput) (domain.ProductType, error) {
	user := token.UserData
	timeMillis := util.CurrentMillis()

	//lakukan insert
	insert := map[string]interface{}{
		"name": input.Name,
		"admin_fkid": user.BusinessId,
		"data_created": timeMillis,
		"data_modified": timeMillis,
		"data_status": "on",
	}
	if input.Code != "" {
		insert["code"] = input.Code
	}
	result, err := r.Insert("products_type", insert)
	if err != nil {
		fmt.Println("error create product type: ", err)
		return domain.ProductType{}, err
	}

	id, _ := result.LastInsertId()
	idx := cast.ToString(id)
	return domain.ProductType{
		ID:   idx,
		Name: input.Name,
		Code: input.Code,
	}, err
}

func (r productTypeRepository) Update(input domain.ProductType) (domain.ProductType, error) {
	user := token.UserData
	timeMillis := util.CurrentMillis()

	//lakukan update
	data := map[string]interface{}{
		"name": input.Name,
		"code": nil,
		"data_modified": timeMillis,
	}
	if input.Code != "" {
		data["code"] = input.Code
	}
	_, err := r.Updates("products_type", data, map[string]interface{}{
		"products_type_id": input.ID,
		"admin_fkid": user.BusinessId,
		"data_status": "on",
	})
	if err != nil {
		fmt.Println("error update product type: ", err)
	}
	//fmt.Println(result)
	return domain.ProductType{
		ID:   input.ID,
		Name: input.Name,
		Code: input.Code,
	}, err
}

func (r productTypeRepository) DeleteById(id string) error {
	user := token.UserData

	_, err := r.Deletes("products_type", map[string]interface{}{
		"products_type_id": id,
		"admin_fkid": user.BusinessId,
		"data_status": "on",
	})
	if err != nil {
		fmt.Println("error delete product type: ",err)
	}
	return err
}