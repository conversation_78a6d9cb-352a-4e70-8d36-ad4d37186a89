package mysql

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	mysql "uniqdev/api-pos-web/core/mysql"
	"uniqdev/api-pos-web/core/util"
	"uniqdev/api-pos-web/core/util/cast"
	domain "uniqdev/api-pos-web/domain"
)

type authKrakendRepository struct {
	mysql.Repository
}

func AuthKrakendRepository(db *sql.DB) domain.AuthKrakendRepository {
	return &authKrakendRepository{mysql.Repository{Conn: db}}
}

func (r authKrakendRepository) SaveRefreshToken(id, tokenDataOnJson, bcryptHash string, expired int64) error {
	_, err := r.Insert("users_session", map[string]interface{}{
		"id":         id + ".refresh",
		"data":       tokenDataOnJson,
		"token":      bcryptHash,
		"expired_at": expired,
		"timestamp":  expired,
	})

	return err
}

func (r authKrakendRepository) FindRefreshToken(refreshToken string) (domain.User, error) {
	var response domain.User

	//find token
	result, err := r.Query("SELECT * FROM users_session WHERE id=? AND expired_at >= ?", refreshToken, util.CurrentTime()).Map()
	if err != nil {
		return response, err
	}
	if len(result) == 0 {
		fmt.Printf("No result of find refresh token: %s\n", refreshToken)
		return response, errors.New("404")
	}

	//compare hash token data
	err2 := util.BcryptVerify(cast.ToString(result["data"]), cast.ToString(result["token"]))
	if err2 != nil {
		fmt.Printf("failed to compare hash of refresh token: %s\n", refreshToken)
		return response, errors.New("401")
	}

	//decode token to User
	refreshTokenData := cast.ToString(result["data"])

	var user domain.User
	var claims map[string]interface{}
	claims, _ = cast.JsonToMap(refreshTokenData)
	dataUser, _ := json.Marshal(claims["data"])
	cast.JsonToStruct(dataUser, &user)

	if user.UserId == "" {
		fmt.Println("empty user data")
		return response, errors.New("404")
	}

	go func() {
		r.Deletes("users_session", map[string]interface{}{
			"id": refreshToken,
		})
	}()

	return user, err
}
