package usecase

import (
	"context"
	"errors"
	"fmt"
	"uniqdev/api-pos-web/core/apple_helper"
	"uniqdev/api-pos-web/core/google"
	"uniqdev/api-pos-web/core/log"
	"uniqdev/api-pos-web/core/util/cast"

	"github.com/Timothylock/go-signin-with-apple/apple"
	"github.com/golang-jwt/jwt/v4"
)

type AuthResult struct {
	Email string
}

type SocialMediaAuth interface {
	ValidateToken(token string) (AuthResult, error)
}

type AuthWithGoogle struct{}

func (a AuthWithGoogle) ValidateToken(token string) (AuthResult, error) {
	tokenClaim, err := google.VerifyIdToken(token)
	if log.IfError(err) {
		return AuthResult{}, err
	}

	fmt.Println("token claims:", tokenClaim)
	if cast.ToString(tokenClaim["email"]) == "" {
		return AuthResult{}, errors.New("401")
	}

	userEmail := cast.ToString(tokenClaim["email"])
	return AuthResult{Email: userEmail}, nil
}

type AuthWithApple struct{}

func (a AuthWithApple) ValidateToken(token string) (AuthResult, error) {
	cfg := apple_helper.GetAppleConfig()
	teamId := cfg.TeamId
	clientId := cfg.ClientId
	keyId := cfg.KeyId
	keySecret := cfg.KeySecret
	fmt.Println(cast.ToString(cfg))

	secret, err := apple.GenerateClientSecret(keySecret, teamId, clientId, keyId)
	if log.IfError(err) {
		return AuthResult{}, err
	}

	vReq := apple.AppValidationTokenRequest{
		ClientID:     clientId,
		ClientSecret: secret,
		Code:         token,
	}

	client := apple.New()
	var resp apple.AppValidationTokenRequest
	err = client.VerifyAppToken(context.Background(), vReq, &resp)
	if err != nil {
		fmt.Println("VerifyAppToken: ", err)
		return AuthResult{}, err
	}

	tokenJwt, err := jwt.Parse(token, func(t *jwt.Token) (interface{}, error) {
		return "", nil
	})
	log.IfError(err)
	if claim, ok := tokenJwt.Claims.(jwt.MapClaims); ok {
		fmt.Println("claim", cast.ToString(claim))
		email := cast.ToString(claim["email"])
		if email == "" {
			log.IfError(fmt.Errorf("failed to get email from sign in with apple"))
		}
		return AuthResult{Email: email}, nil
	}

	fmt.Println("resp: ", cast.ToString(resp))
	unique, _ := apple.GetUniqueID(resp.Code)

	// Voila!
	fmt.Println("unique: ", unique)
	return AuthResult{}, nil
}
