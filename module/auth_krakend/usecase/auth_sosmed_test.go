package usecase

import (
	"reflect"
	"testing"
	"uniqdev/api-pos-web/core/apple_helper"
)

func TestAuthWithApple_ValidateToken(t *testing.T) {
	apple_helper.SetAppleConfig("appleId", "c", "k", "")
	toke1 := "eyJraWQiOiJmaDZCczhDIiwiYWxnIjoiUlMyNTYifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jpY0YyCd04aDd2wXANbl_zWXIvvEJvC03tTG0Q570uOs3yZORXGXJOsIIVoYIZwDcKWLhIeknRilPao8XS2qwFveYW-IwpsucVncBMZgbt5SKsa5pndqO0oJoK_2SMUXxuch3kDdedBoOgKtg59xvvxt-XifPns5MSR_0heP7BX1jYuuJUKYhLRcPvHINXgdP7L8RMTCHTcH4qpmatSslaGqXLAIl2A7f23Btvb7JpeOHR-nhcyEtQ7xIBjj-tVU_k36m633rQNYsvQfM4Daf0_Ph3RSgr4tGTa2sATEU9k6cWWmCUGa1bQO7QZp-yQsk1RdEZ0RxQLGUqyZdEn04Q"
	type args struct {
		token string
	}
	tests := []struct {
		name    string
		a       AuthWithApple
		args    args
		want    AuthResult
		wantErr bool
	}{
		{"test1", AuthWithApple{}, args{token: toke1}, AuthResult{}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AuthWithApple{}
			got, err := a.ValidateToken(tt.args.token)
			if (err != nil) != tt.wantErr {
				t.Errorf("AuthWithApple.ValidateToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AuthWithApple.ValidateToken() = %v, want %v", got, tt.want)
			}
		})
	}
}
