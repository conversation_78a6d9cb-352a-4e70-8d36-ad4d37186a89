package mysql

import (
	"database/sql"
	"fmt"
	mysql "uniqdev/api-pos-web/core/mysql"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/token"
	domain "uniqdev/api-pos-web/domain"
)

type productUnitRepository struct {
	mysql.Repository
}

func ProductUnitRepository(db *sql.DB) domain.ProductUnitRepository {
	return &productUnitRepository{mysql.Repository{Conn: db}}
}

func (r productUnitRepository) selectQuery() string {
	return "SELECT unit_id AS id, name, description FROM unit "
}

func (r productUnitRepository) FetchById(id string) (domain.ProductUnit, error) {
	user := token.UserData

	var result domain.ProductUnit
	err := r.Query(
		r.selectQuery()+` WHERE unit_id=? AND data_status=? AND admin_fkid=?`, id, "on", user.BusinessId).Model(&result)
	if err != nil {
		fmt.Printf("error fetch product_unit by id: %v\n", err)
	}

	return result, err
}

func (r productUnitRepository) Add(input domain.ProductUnitInput) (domain.ProductUnit, error) {
	user := token.UserData

	//insert
	data := map[string]interface{}{
		"name": input.Name,
		"description": input.Description,
		"admin_fkid": user.BusinessId,
		"data_status": "on",
	}
	result, err := r.Insert("unit", data)
	if err != nil {
		fmt.Printf("error create purchase report category: %v\n", err)
		return domain.ProductUnit{}, err
	}

	id, _ := result.LastInsertId()
	return domain.ProductUnit{
		ID:          cast.ToString(id),
		Name:        input.Name,
		Description: input.Description,
	}, err
}

func (r productUnitRepository) Update(input domain.ProductUnit) (domain.ProductUnit, error) {
	user := token.UserData

	data := map[string]interface{}{
		"name": input.Name,
		"description": input.Description,
	}
	_, err := r.Updates("unit", data, map[string]interface{}{
		"unit_id": input.ID,
		"admin_fkid": user.BusinessId,
		"data_status": "on",
	})
	if err != nil {
		fmt.Printf("fail update: unit: %v \n:: %v", input.ID, err)
	}
	return input, err
}

func (r productUnitRepository) DeleteById(id string) error {
	user := token.UserData

	_, err := r.Deletes("unit", map[string]interface{}{
		"unit_id": id,
		"admin_fkid": user.BusinessId,
		"data_status": "on",
	})
	if err != nil {
		fmt.Printf("fail delete unit: %v \n:: %v \n", id, err)
	}
	return err
}
