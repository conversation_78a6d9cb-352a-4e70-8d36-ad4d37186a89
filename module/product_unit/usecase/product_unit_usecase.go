package usecase

import (
	"encoding/json"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/validation"
	domain "uniqdev/api-pos-web/domain"
)

type productUnitUseCase struct {
	repo domain.ProductUnitRepository
}

func ProductUnitUseCase(repository domain.ProductUnitRepository) domain.ProductUnitUseCase {
	return &productUnitUseCase{repository}
}

func (u productUnitUseCase) FetchById(id string) (domain.ProductUnit, error) {
	return u.repo.FetchById(id)
}

func (u productUnitUseCase) Add(input domain.ProductUnitInput) (domain.ProductUnit, error) {
	var (
		result domain.ProductUnit
		err error
	)

	errList := validation.Struct(input)
	if validation.IsError(errList) {
		return result, cast.ToError(errList)
	}

	result, err = u.repo.Add(input)
	return result, err
}

func (u productUnitUseCase) Update(input domain.ProductUnit) (domain.ProductUnit, error) {
	var result domain.ProductUnit
	var inputValidation domain.ProductUnitInput

	//check id is valid
	rst, _ := u.repo.FetchById(input.ID)
	if rst.ID == "" {
		return result, cast.ToError("404")
	}

	//validation
	b, _ := json.Marshal(input)
	jsonString := cast.ToString(b)
	cast.JsonToStruct(jsonString, &inputValidation)

	errList := validation.Struct(inputValidation)
	if validation.IsError(errList) {
		return result, cast.ToError(errList)
	}

	//process update
	result, err := u.repo.Update(input)
	return result, err
}

func (u productUnitUseCase) DeleteById(id string) error {
	//check id is valid
	result, err := u.repo.FetchById(id)
	if err != nil {
		return err
	}
	if result.ID == "" {
		return cast.ToError("404")
	}

	//delete process
	err = u.repo.DeleteById(id)
	return err
}
