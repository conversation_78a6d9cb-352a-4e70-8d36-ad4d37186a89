package usecase

import (
	"encoding/base64"
	"errors"
	"fmt"
	"github.com/dgrijalva/jwt-go"
	"golang.org/x/crypto/bcrypt"
	"strings"
	"time"
	"uniqdev/api-pos-web/core/util"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/validation"
	"uniqdev/api-pos-web/domain"
)

type authUseCase struct {
	domain.AuthRepository
}

func AuthUseCase(repository domain.AuthRepository) domain.AuthUseCase {
	return &authUseCase{repository}
}

func (u authUseCase) CreateToken(lg domain.AuthInputLogin) (domain.UserToken, error) {
	var userToken domain.UserToken

	//validation
	errList := validation.Struct(lg)
	if validation.IsError(errList) {
		return userToken, cast.ToError(errList)
	}

	//1. check user from DB
	//-- check user is valid
	user, pass, err := u.AuthRepository.FindAccount("admin", lg.Email)
	if user.UserId == "" {
		user, pass, err = u.AuthRepository.FindAccount("employee", lg.Email)
	}
	if user.UserId == "" {
		//account not found
		return userToken, errors.New("404")
	}

	//2. validate user's password
	err = bcrypt.CompareHashAndPassword([]byte(pass), []byte(lg.Password))
	if err != nil {
		//invalid email or password
		return userToken, errors.New("401")
	}

	//3. if valid, get user's outlet
	outletAccess, _ := u.AuthRepository.GetOutletAccess(user)

	//4. then create token payload
	token := jwt.New(jwt.SigningMethodHS256)
	//expired := time.Now().Add(time.Hour * 72).Unix()
	expired := time.Now().Add(time.Minute * 3).Unix() //for testing
	claims := token.Claims.(jwt.MapClaims)
	claims["exp"] = expired
	claims["role"] = map[string]interface{}{
		"outlet_access": outletAccess,
	}
	claims["data"] = user

	// Generate encoded token and send it as response.
	generatedToken, err := token.SignedString([]byte("secret"))

	//generate refresh token
	refreshToken := jwt.New(jwt.SigningMethodHS256)
	refreshTokenExpired := time.Now().Add(time.Hour * 168).Unix()
	refreshClaims := refreshToken.Claims.(jwt.MapClaims)
	refreshClaims["iss"] = "login"
	refreshClaims["exp"] = refreshTokenExpired
	refreshClaims["data"] = user
	generatedRefreshToken, err := refreshToken.SignedString([]byte("secret"))

	refreshTokenJTI := util.HashMd5(generatedRefreshToken)
	refreshClaims["jti"] = refreshTokenJTI
	generatedRefreshToken, err = refreshToken.SignedString([]byte("secret"))

	//save refresh token to DB
	u.AuthRepository.SaveRefreshToken(generatedRefreshToken)

	//prepare output
	userToken.User = user
	userToken.UserRole = domain.UserRole{OutletAccess: outletAccess}
	userToken.Token = domain.Token{
		Token:   generatedToken,
		Expired: expired,
		Type:    "Bearer",
	}
	userToken.RefreshToken = domain.RefreshToken{
		Token:   generatedRefreshToken,
		Expired: refreshTokenExpired,
	}

	return userToken, err
}

func (u authUseCase) TokenByPHPSession(sessionID string) (domain.UserToken, error) {
	var userToken domain.UserToken

	//validation
	if sessionID == "" {
		return userToken, errors.New("401")
	}

	//find account with session
	user, err := u.AuthRepository.FindSession(sessionID)
	if user.UserId == "" {
		return userToken, errors.New("404")
	}

	currentUserData, err2 := u.AuthRepository.FindAccountByUserTypeUserId(user.UserType, user.UserId)
	err = err2

	//generate token
	outletAccess, _ := u.AuthRepository.GetOutletAccess(user)

	//4. then create token payload
	token := jwt.New(jwt.SigningMethodHS256)
	expired := time.Now().Add(time.Minute * 3).Unix()
	claims := token.Claims.(jwt.MapClaims)
	claims["exp"] = expired
	claims["role"] = map[string]interface{}{
		"outlet_access": outletAccess,
	}
	claims["data"] = currentUserData

	// Generate encoded token and send it as response.
	generatedToken, err := token.SignedString([]byte("secret"))

	//prepare output
	userToken.User = user
	userToken.UserRole = domain.UserRole{OutletAccess: outletAccess}
	userToken.Token = domain.Token{
		Token:   generatedToken,
		Expired: expired,
		Type:    "Bearer",
	}

	return userToken, err
}

func (u authUseCase) TokenByRefreshToken(refreshToken string) (domain.UserToken, error) {
	var userToken domain.UserToken

	//validation
	if refreshToken == "" {
		fmt.Println("refreshToken sent by client is empty...")
		return userToken, errors.New("401")
	}

	//find saved token
	tokenBody := ""
	var claims1 map[string]interface{}

	//decode token
	tokenData := strings.Split(refreshToken, ".")
	if len(tokenData) == 3 {
		tokenBody = tokenData[1]

		//get token data
		decoded, _ := base64.RawStdEncoding.DecodeString(tokenBody)
		decodedJson := string(decoded)
		claims1, _ = cast.JsonToMap(decodedJson)
	}

	//find token data on DB by refresh token JTI
	refreshTokenID := cast.ToString(claims1["jti"])
	user, err := u.AuthRepository.FindRefreshToken(refreshTokenID)
	if err != nil {
		return userToken, err
	}

	fmt.Println(user)
	if user.UserId == "" {
		return domain.UserToken{}, errors.New("404")
	}

	//generate new token
	outletAccess, _ := u.AuthRepository.GetOutletAccess(user)

	//4. then create token payload
	token := jwt.New(jwt.SigningMethodHS256)
	expired := time.Now().Add(time.Hour * 72).Unix()
	claims := token.Claims.(jwt.MapClaims)
	claims["exp"] = expired
	claims["role"] = map[string]interface{}{
		"outlet_access": outletAccess,
	}
	claims["data"] = user

	// Generate encoded token and send it as response.
	generatedToken, err := token.SignedString([]byte("secret"))

	//generate refresh token
	refreshToken2 := jwt.New(jwt.SigningMethodHS256)
	refreshTokenExpired := time.Now().Add(time.Hour * 168).Unix()
	refreshClaims := refreshToken2.Claims.(jwt.MapClaims)
	refreshClaims["iss"] = "login"
	refreshClaims["exp"] = refreshTokenExpired
	refreshClaims["data"] = user
	generatedRefreshToken, err := refreshToken2.SignedString([]byte("secret"))

	refreshTokenJTI := util.HashMd5(generatedRefreshToken)
	refreshClaims["jti"] = refreshTokenJTI
	generatedRefreshToken, err = refreshToken2.SignedString([]byte("secret"))

	//save refresh token to DB
	u.AuthRepository.SaveRefreshToken(generatedRefreshToken)

	//prepare output
	userToken.User = user
	userToken.UserRole = domain.UserRole{OutletAccess: outletAccess}
	userToken.Token = domain.Token{
		Token:   generatedToken,
		Expired: expired,
		Type:    "Bearer",
	}
	userToken.RefreshToken = domain.RefreshToken{
		Token:   generatedRefreshToken,
		Expired: refreshTokenExpired,
	}

	return userToken, err
}
