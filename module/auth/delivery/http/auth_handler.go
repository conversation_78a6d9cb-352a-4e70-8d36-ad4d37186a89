package http

import (
	"fmt"
	v2 "github.com/gofiber/fiber/v2"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/validation"
	domain "uniqdev/api-pos-web/domain"
)

type authHandler struct {
	domain.AuthUseCase
}

func AuthHandler(app *v2.App, useCase domain.AuthUseCase) {
	handler := &authHandler{useCase}
	app.Post("/v1/auth/login", handler.Login)
	app.Post("v1/auth/refresh", handler.RefreshToken)
	app.Get("/v1/auth/session", handler.PHPSession)
	app.Get("/v1/auth/remember", handler.RememberMe)

}

func (h authHandler) Login(c *v2.Ctx) error {
	var inputLogin domain.AuthInputLogin
	c.BodyParser(&inputLogin)

	// process create token
	user, err := h.AuthUseCase.CreateToken(inputLogin)
	if err != nil {
		errStr := cast.ToString(err)
		if validation.IsJson(errStr) {
			c.Status(400)
			return c.JSON(cast.JsonToStruct(errStr, inputLogin))
		}
		switch errStr {
		case "401":
			return c.SendStatus(401)
		case "404":
			return c.SendStatus(404)
		default:
			fmt.Println("login error: ", err)
			return c.SendStatus(422)
		}
	}

	return c.JSON(user)
}

func (h authHandler) PHPSession(c *v2.Ctx) error {
	var user domain.UserToken
	sID := c.Request().Header.Peek("Authorization")
	sessID := cast.ToString(sID)
	fmt.Println("generate token with session: ", sessID)
	user, err := h.AuthUseCase.TokenByPHPSession(sessID)
	if err != nil {
		errStr := cast.ToString(err)
		switch errStr {
		case "401":
			return c.SendStatus(401)
		case "404":
			return c.SendStatus(404)
		default:
			fmt.Println("login error: ", err)
			return c.SendStatus(422)
		}
	}
	return c.JSON(user)
}

func (h authHandler) RememberMe(c *v2.Ctx) error {
	var user domain.UserToken
	rID := c.Request().Header.Peek("Authorization")
	rememberID := cast.ToString(rID)
	fmt.Println("generate token with session: ", rememberID)
	user, err := h.AuthUseCase.TokenByPHPSession(rememberID)
	if err != nil {
		errStr := cast.ToString(err)
		switch errStr {
		case "401":
			return c.SendStatus(401)
		case "404":
			return c.SendStatus(404)
		default:
			fmt.Println("login error: ", err)
			return c.SendStatus(422)
		}
	}
	return c.JSON(user)
}

func (h authHandler) RefreshToken(c *v2.Ctx) error {
	refreshToken := c.FormValue("refresh_token")
	userToken, err := h.AuthUseCase.TokenByRefreshToken(refreshToken)
	if err != nil {
		errStr := cast.ToString(err)
		switch errStr {
		case "401":
			return c.SendStatus(401)
		case "404":
			return c.SendStatus(404)
		default:
			fmt.Println("create token from refreshtoken error: ", err)
			return c.SendStatus(422)
		}
	}

	return c.JSON(userToken)
}
