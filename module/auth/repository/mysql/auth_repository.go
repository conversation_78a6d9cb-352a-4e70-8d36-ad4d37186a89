package mysql

import (
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"strings"
	"time"
	mysql "uniqdev/api-pos-web/core/mysql"
	"uniqdev/api-pos-web/core/util"
	"uniqdev/api-pos-web/core/util/cast"
	domain "uniqdev/api-pos-web/domain"

	"github.com/yvasiyarov/php_session_decoder"
)

type authRepository struct {
	mysql.Repository
}

func AuthRepository(db *sql.DB) domain.AuthRepository {
	return &authRepository{mysql.Repository{Conn: db}}
}

func (r authRepository) FindAccount(userType, email string) (domain.User, string, error) {
	var user domain.User
	var password string
	var err error
	var result map[string]interface{}

	if userType == "admin" {
		result, err = r.Query(`
		SELECT email, password, phone, name,
			admin_id AS user_id,
			admin_id AS business_id,
			'admin' AS user_type,
			account_id AS account_id
		FROM admin
		WHERE email=? AND activation_status=?`, email, "activated").Map()
	}
	if userType == "employee" {
		result, err = r.Query(`
		SELECT email, password, phone, name,
			employee_id AS user_id,
			admin_fkid AS business_id,
			'employee' AS user_type,
			account_id AS account_id
		FROM employee
		WHERE email=? AND access_status_web=? AND data_status=?`, email, "activated", "on").Map()
	}
	if userType == "hrm" {
		result, err = r.Query(`
		SELECT a.email, a.password, a.phone, he.name,
			hrm_employee_id AS user_id,
			admin_fkid AS business_id,
			'hrm' AS user_type,
			account_id AS account_id
		FROM hrm_employee he 
		join accounts a on a.id = he.account_id
		WHERE a.email=? AND data_status=?`, email, "on").Map()
	}

	password = cast.ToString(result["password"])
	cast.MapToStruct(result, &user)
	return user, password, err
}

func (r authRepository) GetOutletAccess(user domain.User) (string, error) {
	var result []map[string]interface{}
	var err error
	var outletAccess string

	if user.UserType == "admin" {
		result, err = r.Query("SELECT outlet_id AS id FROM outlets WHERE admin_fkid=? AND data_status=?",
			user.BusinessId, "on").ArrayMap()
	} else if user.UserType == "employee" {
		result, err = r.Query(`SELECT eo.outlet_fkid AS id, e.admin_fkid AS admin_id
					FROM employee_outlet eo
					LEFT JOIN outlets o ON eo.outlet_fkid=o.outlet_id
					LEFT JOIN employee e ON eo.employee_fkid=e.employee_id
					WHERE o.data_status=?
					AND o.admin_fkid=?
					AND eo.employee_fkid=?`, "on", user.BusinessId, user.UserId).ArrayMap()
	} else if user.UserType == "hrm" {
		result, err = r.Query("SELECT outlet_fkid AS id FROM hrm_employee WHERE hrm_employee_id=? AND admin_fkid=? AND data_status=?",
			user.UserId, user.BusinessId, "on").ArrayMap()
	}

	//generate outlet access
	var outletAccessIds = make([]string, 0)
	for _, data := range result {
		outletAccessIds = append(outletAccessIds, cast.ToString(data["id"]))
	}
	outletAccess = strings.Join(outletAccessIds, ",")

	return outletAccess, err
}

func (r authRepository) FindSession(sessionID string) (domain.User, error) {
	var user domain.User
	result, err := r.Query(
		"SELECT *, timestamp+7500 AS timestamp_expired "+
			"FROM users_session WHERE id=? AND data is not null HAVING timestamp_expired>? ", sessionID, time.Now().Unix(),
	).Map()
	if err != nil && len(result) > 0 {
		fmt.Printf("PHPSession: %s :: not found.\n", sessionID)
		return user, err
	}

	//get php session data
	sessionRawData := cast.ToString(result["data"])
	sessionData, err := php_session_decoder.NewPhpDecoder(sessionRawData).Decode()
	if sessionData["user_logged"] == false {
		return user, errors.New("user is not logged in this session")
	}

	//create user data based on PHPsession information
	user = domain.User{
		Name:       cast.ToString(sessionData["user_name"]),
		Email:      cast.ToString(sessionData["email"]),
		Phone:      cast.ToString(sessionData["phone"]),
		UserType:   cast.ToString(sessionData["user_type"]),
		UserId:     cast.ToString(sessionData["user_id"]),
		BusinessId: cast.ToString(sessionData["admin_id"]),
		AccountId:  "",
	}

	return user, err
}

func (r authRepository) FindAccountByUserTypeUserId(userType, userId string) (domain.User, error) {
	var result domain.User
	var err error

	columnName := "account_id"
	if os.Getenv("ENV") == "production" {
		//columnName = "' '"
	}

	if userType == "admin" {
		err = r.Query(`
		SELECT email, password, phone, name,
			admin_id AS user_id,
			admin_id AS business_id,
			'admin' AS user_type,
			`+columnName+` AS account_id
		FROM admin
		WHERE admin_id=? AND activation_status=?`, userId, "activated").Model(&result)
	}
	if userType == "employee" {
		err = r.Query(`
		SELECT email, password, phone, name,
			employee_id AS user_id,
			admin_fkid AS business_id,
			'employee' AS user_type,
			`+columnName+` AS account_id
		FROM employee
		WHERE employee_id=? AND access_status_web=? and data_status=?`, userId, "activated", "on").Model(&result)
	}
	if err != nil {
		log.Println("error query FindAccountByUserTypeUserId: ", err)
	}

	return result, err
}

func (r authRepository) SaveRefreshToken(generatedRefreshToken string) error {
	tokenBody := ""
	var claims map[string]interface{}

	//decode token
	tokenData := strings.Split(generatedRefreshToken, ".")
	if len(tokenData) == 3 {
		tokenBody = tokenData[1]

		//get token data
		decoded, _ := base64.RawStdEncoding.DecodeString(tokenBody)
		decodedJson := string(decoded)
		claims, _ = cast.JsonToMap(decodedJson)
	}

	//save token to DB
	hashedData, _ := util.BcryptHash(generatedRefreshToken)
	if len(claims) > 0 {
		_, err := r.Insert("users_session", map[string]interface{}{
			"id":         claims["jti"],
			"timestamp":  claims["exp"],
			"data":       generatedRefreshToken,
			"token":      hashedData,
			"expired_at": claims["exp"],
		})
		return err
	}

	return errors.New("error: save refresh_token failed because claims empty.")
}

func (r authRepository) FindRefreshToken(refreshTokenJTI string) (domain.User, error) {
	//get token
	result, err := r.Query("SELECT * FROM users_session WHERE id=? AND expired_at >= ?", refreshTokenJTI, util.CurrentTime()).Map()
	if err != nil {
		return domain.User{}, err
	}
	if len(result) == 0 {
		return domain.User{}, errors.New("404")
	}

	//compare hash
	err2 := util.BcryptVerify(cast.ToString(result["data"]), cast.ToString(result["token"]))
	if err2 != nil {
		return domain.User{}, errors.New("401")
	}

	//decode token to User
	refreshToken := cast.ToString(result["data"])

	var user domain.User
	var claims map[string]interface{}
	tokenBody := ""
	tokenData := strings.Split(refreshToken, ".")
	if len(tokenData) == 3 {
		tokenBody = tokenData[1]

		//get token data
		decoded, _ := base64.RawStdEncoding.DecodeString(tokenBody)
		decodedJson := string(decoded)
		claims, _ = cast.JsonToMap(decodedJson)
		dataUser, _ := json.Marshal(claims["data"])

		//token to struct
		cast.JsonToStruct(dataUser, &user)
	}

	if user.UserId == "" {
		return domain.User{}, errors.New("404")
	}

	return user, err
}

func (r authRepository) FindMultiAccountByEmail(email string) ([]domain.User, error) {
	//first check to account table
	account, err := r.Query("select id from acconts where email = ?", email).Map()
	if err != nil {
		return nil, err
	}

	if len(account) == 0 {
		return nil, fmt.Errorf("no account found with email %s", email)
	}

	return r.FindMultiAccount(cast.ToInt(account["id"]))
}

func (r authRepository) FindMultiAccount(accountId int) ([]domain.User, error) {
	var result []domain.User

	//find admin
	admins, err := r.Query("select admin_id, business_name, email, name, phone, activation_status from admin where account_id = ?", accountId).ArrayMap()
	if err != nil {
		return nil, err
	}

	for _, user := range admins {
		result = append(result, domain.User{
			Name:                     cast.ToString(user["name"]),
			Email:                    cast.ToString(user["email"]),
			Phone:                    cast.ToString(user["phone"]),
			UserType:                 "admin",
			UserId:                   cast.ToString(user["admin_id"]),
			BusinessId:               cast.ToString(user["admin_id"]),
			BusinessName:             cast.ToString(user["business_name"]),
			AccountId:                cast.ToString(accountId),
			BusinessActivationStatus: cast.ToString(user["activation_status"]),
		})
	}

	employees, err := r.Query("select e.employee_id, e.name, e.email, e.phone, admin_fkid, a.business_name, a.activation_status from employee e join admin a on a.admin_id = e.admin_fkid where e.account_id = ?", accountId).ArrayMap()
	if err != nil {
		return nil, err
	}

	for _, user := range employees {
		result = append(result, domain.User{
			Name:                     cast.ToString(user["name"]),
			Email:                    cast.ToString(user["email"]),
			Phone:                    cast.ToString(user["phone"]),
			UserType:                 "employee",
			UserId:                   cast.ToString(user["employee_id"]),
			BusinessId:               cast.ToString(user["admin_fkid"]),
			BusinessName:             cast.ToString(user["business_name"]),
			AccountId:                cast.ToString(accountId),
			BusinessActivationStatus: cast.ToString(user["activation_status"]),
		})
	}

	return result, nil
}

func (r authRepository) FindAccountById(userType string, userId string) (domain.User, error) {
	var user map[string]interface{}
	var err error

	if userType == "admin" {
		user, err = r.Query("select name, email, phone, account_id, admin_id from admin where admin_id = ?", userId).Map()
	} else {
		user, err = r.Query("select name, email, phone, account_id, admin_fkid as admin_id from employee where employee_id = ?", userId).Map()
	}
	if err != nil {
		return domain.User{}, err
	}

	return domain.User{
		Name:       cast.ToString(user["name"]),
		Email:      cast.ToString(user["email"]),
		Phone:      cast.ToString(user["phone"]),
		UserType:   userType,
		UserId:     userId,
		BusinessId: cast.ToString(user["admin_id"]),
		AccountId:  cast.ToString(user["account_id"]),
	}, nil
}
