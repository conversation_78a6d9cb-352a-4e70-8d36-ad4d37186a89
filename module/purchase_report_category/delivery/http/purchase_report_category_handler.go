package http

import (
	v2 "github.com/gofiber/fiber/v2"
	"uniqdev/api-pos-web/core/model"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/validation"
	domain "uniqdev/api-pos-web/domain"
)

type purchaseReportCategoryHandler struct {
	domain.PurchaseReportCategoryUseCase
}

func PurchaseReportCategoryHandler(app *v2.App, useCase domain.PurchaseReportCategoryUseCase) {
	handler := &purchaseReportCategoryHandler{useCase}
	path := "/v1/product-categories/purchase-report-category"
	app.Post(path, handler.Create)
	app.Get(path+"/:id", handler.FetchById)
	app.Put(path+"/:id", handler.Update)
	app.Delete(path+"/:id", handler.DeleteById)
}

func (h purchaseReportCategoryHandler) FetchById(c *v2.Ctx) error {
	id := c.Params("id")
	result, err := h.PurchaseReportCategoryUseCase.FetchById(id)
	if result.ID != "" {
		c.Status(200)
		return c.JSON(model.ResponseOK{Message: "Success", Data: result})
	}
	if err != nil {
		c.Status(422)
		return c.JSON(model.ResponseMessage{Message: "Failed to Process Data"})
	}

	//data not found
	c.Status(404)
	return c.JSON(model.ResponseMessage{Message: "Data Not Found"})
}

func (h purchaseReportCategoryHandler) Create(c *v2.Ctx) error {
	var input domain.PurchaseReportCategoryInput
	c.BodyParser(&input)

	//process create
	result, err := h.PurchaseReportCategoryUseCase.Add(input)
	if err != nil {
		errStr := cast.ToString(err)
		if validation.IsJson(errStr) {
			c.Status(400)
			return c.JSON(model.ResponseError{Message: "Invalid Input", Errors: cast.JsonToStruct(errStr, input)})
		}

		c.Status(422)
		return c.JSON(model.ResponseMessage{Message: "Failed to Process Data"})
	}

	c.Status(201)
	return c.JSON(model.ResponseOK{Message: "Create Data Success", Data: result})
}

func (h purchaseReportCategoryHandler) Update(c *v2.Ctx) error {
	var input domain.PurchaseReportCategory
	input.ID = c.Params("id")
	c.BodyParser(&input)

	//process update
	result, err := h.PurchaseReportCategoryUseCase.Update(input)
	if err != nil {
		errStr := cast.ToString(err)
		if validation.IsJson(errStr) {
			c.Status(400)
			return c.JSON(model.ResponseOK{Message: "Invalid Input", Data: cast.JsonToStruct(errStr, input)})
		}
		switch errStr {
		case "404":
			//data not found
			c.Status(404)
			return c.JSON(model.ResponseMessage{Message: "Data Not Found"})
		default:
			c.Status(422)
			return c.JSON(model.ResponseMessage{Message: "Failed to Process Data"})
		}
	}

	c.Status(200)
	return c.JSON(model.ResponseOK{Message: "Update Data Success", Data: result})
}

func (h purchaseReportCategoryHandler) DeleteById(c *v2.Ctx) error {
	id := c.Params("id")
	err := h.PurchaseReportCategoryUseCase.DeleteById(id)
	if err != nil {
		errStr := cast.ToString(err)
		switch errStr {
		case "404":
			c.Status(404)
			return c.JSON(model.ResponseMessage{Message: "Data Not Found"})
		default:
			c.Status(422)
			return c.JSON(model.ResponseMessage{Message: "Failed to Process Data"})
		}
	}

	c.Status(200)
	return c.JSON(model.ResponseMessage{Message: "Delete Data Success"})
}
