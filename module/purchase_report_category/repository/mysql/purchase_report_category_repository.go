package mysql

import (
	"database/sql"
	"fmt"
	mysql "uniqdev/api-pos-web/core/mysql"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/token"
	domain "uniqdev/api-pos-web/domain"
)

type purchaseReportCategoryRepository struct {
	mysql.Repository
}

func PurchaseReportCategoryRepository(db *sql.DB) domain.PurchaseReportCategoryRepository {
	return &purchaseReportCategoryRepository{mysql.Repository{Conn: db}}
}

func (r purchaseReportCategoryRepository) selectQuery() string {
	return "SELECT purchase_report_category_id AS id, name, is_operationalcost FROM purchase_report_category "
}

func (r purchaseReportCategoryRepository) FetchById(id string) (domain.PurchaseReportCategory, error) {
	user := token.UserData

	rst, err := r.Query(
		r.selectQuery() + ` WHERE purchase_report_category_id=? AND data_status=? AND admin_fkid=?`,
		id, "on", user.BusinessId).Map()
	if err != nil {
		fmt.Printf("error fetch purchase_report_category: %v\n", err)
	}
	isOperationalCost := "1"
	if cast.ToString(rst["is_operationalcost"]) != "1" {
		isOperationalCost = "0"
	}
	result := domain.PurchaseReportCategory{
		ID:                cast.ToString(rst["id"]),
		Name:              cast.ToString(rst["name"]),
		IsOperationalCost: isOperationalCost,
	}
	return result, err
}

func (r purchaseReportCategoryRepository) Add(input domain.PurchaseReportCategoryInput) (domain.PurchaseReportCategory, error) {
	user := token.UserData

	if input.IsOperationalCost != "1" && input.IsOperationalCost != "0" {
		input.IsOperationalCost = "0"
	}

	//lakukan insert
	insert := map[string]interface{}{
		"name": input.Name,
		"is_operationalcost": input.IsOperationalCost,
		"admin_fkid": user.BusinessId,
		"data_status": "on",
	}
	result, err := r.Insert("purchase_report_category", insert)
	if err != nil {
		fmt.Printf("error create purchase report category: %v\n", err)
		return domain.PurchaseReportCategory{}, err
	}

	id, _ := result.LastInsertId()
	return domain.PurchaseReportCategory{
		ID:                cast.ToString(id),
		Name:              input.Name,
		IsOperationalCost: input.IsOperationalCost,
	}, err
}

func (r purchaseReportCategoryRepository) Update(input domain.PurchaseReportCategory) (domain.PurchaseReportCategory, error) {
	user := token.UserData

	if input.IsOperationalCost != "1" && input.IsOperationalCost != "0" {
		input.IsOperationalCost = "0"
	}

	data := map[string]interface{}{
		"name": input.Name,
		"is_operationalcost": input.IsOperationalCost,
	}
	_, err := r.Updates("purchase_report_category", data, map[string]interface{}{
		"purchase_report_category_id": input.ID,
		"admin_fkid": user.BusinessId,
		"data_status": "on",
	})
	if err != nil {
		fmt.Printf("fail update: purchase_report_category: %v", err)
	}
	return input, err
}

func (r purchaseReportCategoryRepository) DeleteById(id string) error {
	user := token.UserData

	_, err := r.Deletes("purchase_report_category", map[string]interface{}{
		"purchase_report_category_id": id,
		"admin_fkid": user.BusinessId,
		"data_status": "on",
	})
	if err != nil {
		fmt.Printf("fail delete purchase_report_category: %v \n:: %v \n", id, err)
	}
	return err
}
