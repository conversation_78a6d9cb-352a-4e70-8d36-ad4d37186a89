package usecase

import (
	"encoding/json"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/validation"
	domain "uniqdev/api-pos-web/domain"
)

type purchaseReportCategoryUseCase struct {
	repo domain.PurchaseReportCategoryRepository
}

func PurchaseReportCategoryUseCase(repository domain.PurchaseReportCategoryRepository) domain.PurchaseReportCategoryUseCase {
	return &purchaseReportCategoryUseCase{repository}
}

func (u purchaseReportCategoryUseCase) FetchById(id string) (domain.PurchaseReportCategory, error) {
	return u.repo.FetchById(id)
}

func (u purchaseReportCategoryUseCase) Add(input domain.PurchaseReportCategoryInput) (domain.PurchaseReportCategory, error) {
	var (
		result domain.PurchaseReportCategory
		err error
	)

	errList := validation.Struct(input)
	if validation.IsError(errList) {
		return result, cast.ToError(errList)
	}

	result, err = u.repo.Add(input)
	return result, err
}

func (u purchaseReportCategoryUseCase) Update(input domain.PurchaseReportCategory) (domain.PurchaseReportCategory, error) {
	var result domain.PurchaseReportCategory
	var inputValidation domain.PurchaseReportCategoryInput

	//check id is valid
	rst, _ := u.repo.FetchById(input.ID)
	if rst.ID == "" {
		return result, cast.ToError("404")
	}

	//validation
	b, _ := json.Marshal(input)
	jsonString := cast.ToString(b)
	cast.JsonToStruct(jsonString, &inputValidation)

	errList := validation.Struct(inputValidation)
	if validation.IsError(errList) {
		return result, cast.ToError(errList)
	}

	//process update
	result, err := u.repo.Update(input)
	return result, err
}

func (u purchaseReportCategoryUseCase) DeleteById(id string) error {
	//check id is valid
	result, err := u.repo.FetchById(id)
	if err != nil {
		return err
	}
	if result.ID == "" {
		return cast.ToError("404")
	}

	//delete process
	err = u.repo.DeleteById(id)
	return err
}
