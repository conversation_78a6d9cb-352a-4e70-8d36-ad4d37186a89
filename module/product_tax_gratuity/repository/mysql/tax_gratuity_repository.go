package mysql

import (
	"database/sql"
	"fmt"
	"strings"
	mysql "uniqdev/api-pos-web/core/mysql"
	"uniqdev/api-pos-web/core/util"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/token"
	domain "uniqdev/api-pos-web/domain"
)

type taxGratuityRepository struct {
	mysql.Repository
}

func TaxGratuityRepository(db *sql.DB) domain.TaxGratuityRepository {
	return &taxGratuityRepository{mysql.Repository{Conn: db}}
}

func (r taxGratuityRepository) selectQuery() string {
	return "SELECT gratuity_id AS id, name, tax_category AS category, tax_status AS active_status, IF(tax_type='nominal',jumlah,CONCAT(jumlah,'%')) AS amount " +
		" FROM gratuity "
}

func (r taxGratuityRepository) FetchById(id string) (domain.TaxGratuity, error) {
	user := token.UserData

	var result domain.TaxGratuity
	err := r.Query(r.selectQuery() + ` WHERE gratuity_id=? AND data_status=? AND admin_fkid=?`, id, "on", user.BusinessId).Model(&result)
	if err != nil {
		fmt.Printf("error fetch taxgratuity: %v\n", err)
	}
	return result, err
}

func (r taxGratuityRepository) Add(input domain.TaxGratuityInput) (domain.TaxGratuity, error) {
	user := token.UserData
	timeMillis := util.CurrentMillis()

	data := map[string]interface{}{
		"name": input.Name,
		"tax_category": input.Category,
		"tax_status": input.ActiveStatus,
		"tax_type": "nominal",
		"jumlah": input.Amount,
		"admin_fkid": user.BusinessId,
		"data_created": timeMillis,
		"data_modified": timeMillis,
		"data_status": "on",
	}
	if strings.HasSuffix(input.Amount, "%") {
		data["tax_type"] = "percentage"
		data["jumlah"] = strings.TrimRight(input.Amount, "%")
	}

	result, err := r.Insert("gratuity", data)
	if err != nil {
		fmt.Println("error create tax gratuity: ", err)
		return domain.TaxGratuity{}, err
	}

	id, _ := result.LastInsertId()
	return domain.TaxGratuity{
		ID:           cast.ToString(id),
		Name:         input.Name,
		Category:     input.Category,
		ActiveStatus: input.ActiveStatus,
		Amount:       input.Amount,
	}, err
}

func (r taxGratuityRepository) Update(id string, input domain.TaxGratuityInput) (domain.TaxGratuity, error) {
	user := token.UserData
	timeMillis := util.CurrentMillis()

	data := map[string]interface{}{
		"name": input.Name,
		"tax_category": input.Category,
		"tax_status": input.ActiveStatus,
		"tax_type": "nominal",
		"jumlah": input.Amount,
		"data_modified": timeMillis,
	}
	if strings.HasSuffix(input.Amount, "%") {
		data["tax_type"] = "percentage"
		data["jumlah"] = strings.TrimRight(input.Amount, "%")
	}
	_, err := r.Updates("gratuity", data, map[string]interface{}{
		"gratuity_id": id,
		"admin_fkid": user.BusinessId,
		"data_status": "on",
	})
	if err != nil {
		fmt.Printf("fail update: taxgratuity: %v", err)
	}
	return domain.TaxGratuity{
		ID:           id,
		Name:         input.Name,
		Category:     input.Category,
		ActiveStatus: input.ActiveStatus,
		Amount:       input.Amount,
	}, err
}

func (r taxGratuityRepository) DeleteById(id string) error {
	user := token.UserData

	_, err := r.Deletes("gratuity", map[string]interface{}{
		"gratuity_id": id,
		"admin_fkid": user.BusinessId,
		"data_status": "on",
	})
	if err != nil {
		fmt.Printf("fail delete taxgratuity: %v \n:: %v \n", id, err)
	}
	return err
}
