package usecase

import (
	"github.com/go-playground/validator/v10"
	"regexp"
	"strings"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/validation"
	domain "uniqdev/api-pos-web/domain"
)

type taxGratuityUseCase struct {
	repo domain.TaxGratuityRepository
}

func TaxGratuityUseCase(repository domain.TaxGratuityRepository) domain.TaxGratuityUseCase {
	return &taxGratuityUseCase{repository}
}

func (u taxGratuityUseCase) FetchById(id string) (domain.TaxGratuity, error) {
	return u.repo.FetchById(id)
}

func (u taxGratuityUseCase) Add(input domain.TaxGratuityInput) (domain.TaxGratuity, error) {
	var (
		result domain.TaxGratuity
		err error
	)

	errList := u.isValid(input)
	if validation.IsError(errList) {
		return result, cast.ToError(errList)
	}

	result, err = u.repo.Add(input)
	return result, err
}

func (u taxGratuityUseCase) Update(id string, input domain.TaxGratuityInput) (domain.TaxGratuity, error) {
	var (
		result domain.TaxGratuity
		err error
	)

	//check id is valid
	rst, _ := u.repo.FetchById(id)
	if rst.ID == "" {
		return result, cast.ToError("404")
	}

	//validation
	errList := u.isValid(input)
	if validation.IsError(errList) {
		return result, cast.ToError(errList)
	}

	result, err = u.repo.Update(id, input)
	return result, err
}

func (u taxGratuityUseCase) DeleteById(id string) error {
	//check id is valid
	result, err := u.repo.FetchById(id)
	if err != nil {
		return err
	}
	if result.ID == "" {
		return cast.ToError("404")
	}

	//delete process
	err = u.repo.DeleteById(id)
	return err
}

func (u taxGratuityUseCase) isValid(input domain.TaxGratuityInput) map[string]interface{} {
	v, trans := validation.New()
	v.RegisterValidation("contains", func(fl validator.FieldLevel) bool {
		data := fl.Field().String()
		param := ","+fl.Param()+","
		return (!strings.Contains(",", data) && strings.Contains(param, ","+data+","))
	})
	v.RegisterValidation("number", func(fl validator.FieldLevel) bool {
		data := fl.Field().String()
		result, _ := regexp.Match("^\\d+%?$", []byte(data))
		return result
	})
	errList := validation.Lang{Trans: trans}.Struct(v, input)
	return errList
}