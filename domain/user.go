package domain

type User struct {
	Name                     string `json:"name"`
	Email                    string `json:"email"`
	Phone                    string `json:"phone"`
	UserType                 string `json:"user_type"`
	UserId                   string `json:"user_id"`
	BusinessId               string `json:"business_id"`
	BusinessName             string `json:"business_name"`
	AccountId                string `json:"account_id"`
	BusinessActivationStatus string `json:"business_activation_status"`
}

type UserRole struct {
	OutletAccess string `json:"outlet_access"`
}
