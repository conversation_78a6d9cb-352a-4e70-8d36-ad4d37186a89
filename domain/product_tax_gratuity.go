package domain

type TaxGratuity struct {
	ID          	string `json:"id"`
	Name        	string `json:"name"`
	Category 		string `json:"category"`
	ActiveStatus   	string `json:"active_status"`
	Amount      	string `json:"amount"` //kalau percentage, ada tambahan % di akhir nominal
}
type TaxGratuityUseCase interface{
	taxGratuityContract
}
type TaxGratuityRepository interface{
	taxGratuityContract
}

type taxGratuityContract interface {
	FetchById(id string) (TaxGratuity, error)
	Add(input TaxGratuityInput) (TaxGratuity, error)
	Update(id string, input TaxGratuityInput) (TaxGratuity, error)
	DeleteById(id string) (error)
}

type TaxGratuityInput struct {
	Name        	string `json:"name" xml:"name" form:"name" validate:"required,max=30"`
	Category 		string `json:"category" xml:"category" form:"category" validate:"required,contains=discount0x2Cservice0x2Ctax0x2Cvoucher"`
	ActiveStatus   	string `json:"active_status" xml:"active_status" form:"active_status" validate:"required,contains=permanent0x2Ctemp_active0x2Ctemp_deactive"`
	Amount      	string `json:"amount" xml:"amount" form:"amount" validate:"required,number"` //kalau percentage, ada tambahan % di akhir nominal
}