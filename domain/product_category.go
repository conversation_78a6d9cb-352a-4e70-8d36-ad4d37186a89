package domain

type ProductCategory struct{
	ID   string `json:"id"`
	Name string `json:"name"`
	Code string `json:"code"`
}
type productCategoryContract interface {
	FetchById(id string) (ProductCategory, error)
	Add(input ProductCategoryInput) (ProductCategory, error)
	Update(input ProductCategory) (ProductCategory, error)
	DeleteById(id string) (error)
}
type ProductCategoryUseCase interface{
	productCategoryContract
}
type ProductCategoryRepository interface{
	productCategoryContract
	FetchByCode(code string) (ProductCategory, error)
}

type ProductCategoryInput struct {
	Name string `json:"name" xml:"name" form:"name" validate:"required,max=30"`
	Code string `json:"code" xml:"code" form:"code" validate:"max=10,alphanum,is_unique"`
}