package domain

type ProductUnit struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
}
type productUnitContract interface {
	FetchById(id string) (ProductUnit, error)
	Add(input ProductUnitInput) (ProductUnit, error)
	Update(input ProductUnit) (ProductUnit, error)
	DeleteById(id string) (error)
}
type ProductUnitUseCase interface{
	productUnitContract
}
type ProductUnitRepository interface{
	productUnitContract
}

type ProductUnitInput struct {
	Name		string `json:"name" xml:"name" form:"name" validate:"required,max=10"`
	Description string `json:"description" xml:"description" form:"description" validate:"max=30"`
}