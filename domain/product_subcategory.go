package domain

type ProductSubcategory struct{
	ID   string `json:"id"`
	Name string `json:"name"`
	Code string `json:"code"`
}
type productSubcategoryContract interface {
	FetchById(id string) (ProductSubcategory, error)
	Add(input ProductSubcategoryInput) (ProductSubcategory, error)
	Update(input ProductSubcategory) (ProductSubcategory, error)
	DeleteById(id string) (error)
}
type ProductSubcategoryUseCase interface{
	productSubcategoryContract
}
type ProductSubcategoryRepository interface{
	productSubcategoryContract
	FetchByCode(code string) (ProductSubcategory, error)
}

type ProductSubcategoryInput struct {
	Name string `json:"name" xml:"name" form:"name" validate:"required,max=30"`
	Code string `json:"code" xml:"code" form:"code" validate:"max=10,alphanum,is_unique"`
}