package domain

type PurchaseReportCategory struct {
	ID                string `json:"id"`
	Name              string `json:"name"`
	IsOperationalCost string `json:"is_operationalcost"`
}
type purchaseReportCategoryContract interface {
	FetchById(id string) (PurchaseReportCategory, error)
	Add(input PurchaseReportCategoryInput) (PurchaseReportCategory, error)
	Update(input PurchaseReportCategory) (PurchaseReportCategory, error)
	DeleteById(id string) (error)
}
type PurchaseReportCategoryUseCase interface{
	purchaseReportCategoryContract
}
type PurchaseReportCategoryRepository interface{
	purchaseReportCategoryContract
}

type PurchaseReportCategoryInput struct {
	Name              string `json:"name" xml:"name" form:"name" validate:"required,max=30"`
	IsOperationalCost string `json:"is_operationalcost" xml:"is_operationalcost" form:"is_operationalcost" validate:"required"`
}