package domain

type AuthKrakend struct {
	AccessToken         interface{} `json:"access_token"`
	AccessTokenExpired  int64       `json:"access_token_expired"`
	AccessTokenType     string      `json:"access_token_type"`
	RefreshToken        string      `json:"refresh_token"`
	RefreshTokenExpired int64       `json:"refresh_token_expired"`
	Data                struct {
		User     User     `json:"user"`
		UserRole UserRole `json:"user_role"`
	} `json:"data"`
}
type AuthKrakendUseCase interface {
	CreateTokenByLogin(loginInput AuthInputLogin) (AuthKrakend, error)
	CreateTokenByRefreshToken(refreshToken string) (AuthKrakend, error)
	CreateTokenByPHPSession(sessionID string) (AuthKrakend, error)
	GenerateToken(user User) (AuthKrakend, error)
	GenerateRefreshToken(user User) (token string, expired int64)
	FetchUserAccounts(accountId string, email string) (interface{}, error)
	CreateTokenByAccount(userId string, userType string, accountId string) (AuthKrakend, error)
	CreateTokenByLoginSocial(loginSocial LoginSocial, socialMedia string) (AuthKrakend, error)
}
type AuthKrakendRepository interface {
	SaveRefreshToken(id, tokenDataOnJson, bcryptHash string, expired int64) error
	FindRefreshToken(refreshToken string) (User, error)
}
