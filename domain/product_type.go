package domain

type ProductType struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Code string `json:"code"`
}
type productTypeContract interface {
	FetchById(id string) (ProductType, error)
	Add(input ProductTypeInput) (ProductType, error)
	Update(input ProductType) (ProductType, error)
	DeleteById(id string) (error)
}
type ProductTypeUseCase interface{
	productTypeContract
}
type ProductTypeRepository interface{
	productTypeContract
	FetchByCode(code string) (ProductType, error)
}

type ProductTypeInput struct {
	Name string `json:"name" xml:"name" form:"name" validate:"required,max=30"`
	Code string `json:"code" xml:"code" form:"code" validate:"max=10,alphanum,is_unique"`
}