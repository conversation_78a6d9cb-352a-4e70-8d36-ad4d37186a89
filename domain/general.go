package domain

type General struct{}
type GeneralUseCase interface{}
type GeneralRepository interface{}


type Token struct {
	Token   string `json:"token"`
	Expired int64 `json:"expired"`
	Type    string `json:"type"`
}

type RefreshToken struct {
	Token   string `json:"token"`
	Expired int64 `json:"expired"`
}

type UserToken struct {
	User  User  `json:"user"`
	UserRole UserRole `json:"user_role"`
	Token Token `json:"token"`
	RefreshToken RefreshToken `json:"refresh_token"`
}