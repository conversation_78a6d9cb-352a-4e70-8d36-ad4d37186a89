module uniqdev/api-pos-web

go 1.15

require (
	cloud.google.com/go v0.97.0 // indirect
	firebase.google.com/go/v4 v4.6.0
	github.com/DATA-DOG/go-sqlmock v1.5.0
	github.com/Timothylock/go-signin-with-apple v0.0.0-20221018051840-f7d0bffd758f // indirect
	github.com/andybalholm/brotli v1.0.1 // indirect
	github.com/dave/jennifer v1.4.1
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/go-playground/locales v0.13.0
	github.com/go-playground/universal-translator v0.17.0
	github.com/go-playground/validator/v10 v10.4.1
	github.com/go-sql-driver/mysql v1.5.0
	github.com/gofiber/fiber/v2 v2.1.4
	github.com/gofiber/jwt/v2 v2.0.1
	github.com/golang-jwt/jwt/v4 v4.4.2 // indirect
	github.com/golang/protobuf v1.5.2
	github.com/joho/godotenv v1.3.0
	github.com/klauspost/compress v1.11.2 // indirect
	github.com/yvasiyarov/php_session_decoder v0.0.0-20180803065642-a065a3b0b7d1
	golang.org/x/crypto v0.0.0-20201016220609-9e8e0b390897
	golang.org/x/net v0.0.0-20211013171255-e13a2654a71e // indirect
	golang.org/x/oauth2 v0.0.0-20211005180243-6b3c2da341f1 // indirect
	golang.org/x/sys v0.0.0-20211013075003-97ac67df715c // indirect
	golang.org/x/text v0.3.7 // indirect
	google.golang.org/api v0.58.0
	google.golang.org/genproto v0.0.0-20211013025323-ce878158c4d4 // indirect
	google.golang.org/grpc v1.41.0
	google.golang.org/protobuf v1.27.1
	gorm.io/driver/mysql v1.0.2
	gorm.io/gorm v1.20.2
)
